{% load crispy_forms_tags %}
{% load crispy_forms_utils %}

{% specialspaceless %}
{% if formset_tag %}
<form {{ flat_attrs }} method="{{ form_method }}" {% if formset.is_multipart %} enctype="multipart/form-data"{% endif %}>
{% endif %}
    {% if formset_method|lower == 'post' and not disable_csrf %}
        {% csrf_token %}
    {% endif %}

    <div>
        {{ formset.management_form|crispy }}
    </div>

    {% include "bootstrap4/errors_formset.html" %}

    {% for form in formset %}
        {% include "bootstrap4/display_form.html" %}
    {% endfor %}

    {% if inputs %}
        <div class="form-actions">
            {% for input in inputs %}
                {% include "bootstrap4/layout/baseinput.html" %}
            {% endfor %}
        </div>
    {% endif %}
{% if formset_tag %}</form>{% endif %}
{% endspecialspaceless %}
