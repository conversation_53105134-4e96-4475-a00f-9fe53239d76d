from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class SiteConfiguration(models.Model):
    """Site-wide configuration settings"""

    # Company information
    company_name = models.CharField(max_length=200, default="YalaOffice")
    company_address = models.TextField(blank=True)
    company_phone = models.CharField(max_length=15, blank=True)
    company_email = models.EmailField(blank=True)
    company_website = models.URLField(blank=True)

    # Business settings
    default_currency = models.CharField(max_length=10, default="Dh")
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    minimum_order_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Email settings
    order_notification_email = models.EmailField(blank=True)
    support_email = models.EmailField(blank=True)

    # Social media
    facebook_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)

    # Features
    enable_user_registration = models.BooleanField(default=True)
    enable_guest_checkout = models.BooleanField(default=False)
    enable_reviews = models.BooleanField(default=True)
    enable_wishlist = models.BooleanField(default=True)

    # Maintenance
    maintenance_mode = models.BooleanField(default=False)
    maintenance_message = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Site Configuration - {self.company_name}"

    class Meta:
        verbose_name = "Site Configuration"
        verbose_name_plural = "Site Configuration"


class AuditLog(models.Model):
    """Audit log for tracking important actions"""

    ACTION_TYPES = (
        ('create', 'Create'),
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('order_placed', 'Order Placed'),
        ('order_cancelled', 'Order Cancelled'),
        ('payment_received', 'Payment Received'),
        ('stock_updated', 'Stock Updated'),
        ('user_created', 'User Created'),
        ('user_deactivated', 'User Deactivated'),
    )

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    action_type = models.CharField(max_length=50, choices=ACTION_TYPES)
    object_type = models.CharField(max_length=100)  # Model name
    object_id = models.PositiveIntegerField(null=True, blank=True)
    object_repr = models.CharField(max_length=200, blank=True)

    # Details
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    # Additional data
    extra_data = models.JSONField(default=dict, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_name = self.user.get_full_name() if self.user else "Anonymous"
        return f"{user_name} - {self.action_type} - {self.object_type}"

    class Meta:
        ordering = ['-created_at']


class FAQ(models.Model):
    """Frequently Asked Questions"""

    CATEGORIES = (
        ('general', 'General'),
        ('orders', 'Orders'),
        ('payments', 'Payments'),
        ('delivery', 'Delivery'),
        ('returns', 'Returns'),
        ('account', 'Account'),
    )

    question = models.CharField(max_length=500)
    answer = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORIES, default='general')
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.question

    class Meta:
        ordering = ['category', 'order', 'question']


class ContactMessage(models.Model):
    """Contact form messages"""

    STATUS_CHOICES = (
        ('new', 'New'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    )

    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=15, blank=True)
    subject = models.CharField(max_length=200)
    message = models.TextField()

    # User if logged in
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_messages')

    # Response
    response = models.TextField(blank=True)
    responded_at = models.DateTimeField(null=True, blank=True)
    responded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='responded_messages')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.subject}"

    class Meta:
        ordering = ['-created_at']


class Banner(models.Model):
    """Homepage banners and announcements"""

    BANNER_TYPES = (
        ('hero', 'Hero Banner'),
        ('promotion', 'Promotion Banner'),
        ('announcement', 'Announcement'),
        ('category', 'Category Banner'),
    )

    title = models.CharField(max_length=200)
    subtitle = models.CharField(max_length=300, blank=True)
    description = models.TextField(blank=True)
    banner_type = models.CharField(max_length=20, choices=BANNER_TYPES, default='hero')

    # Images
    desktop_image = models.ImageField(upload_to='banners/')
    mobile_image = models.ImageField(upload_to='banners/', blank=True, null=True)

    # Action
    button_text = models.CharField(max_length=50, blank=True)
    button_url = models.URLField(blank=True)

    # Display settings
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['order', '-created_at']
