# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-05-14 16:01+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Azerbaijani <https://hosted.weblate.org/projects/allauth/"
"django-allauth/az/>\n"
"Language: az\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Bu hesab hazırda aktiv deyil."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Siz əsas e-poçt ünvanınızı silə bilməzsiniz."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Bu e-poçt ünvanı artıq bu hesabla əlaqələndirilib."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Göstərdiyiniz e-poçt ünvanı və ya şifrə düzgün deyil."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Göstərdiyiniz istifadəçi adı və yaxud şifrə düzgün deyil."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "İstifadəçi artıq bu e-poçt ünvanı ilə qeydiyyatdan keçib."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Zəhmət olmasa cari şifrənizi yazın."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Yanlış kod."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Yanlış şifrə."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Etibarsız və ya müddəti bitmiş açar."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Etibarsız giriş."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Şifrə sıfırlama tokeni yanlışdır."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Siz %d-dən çox e-poçt ünvanı əlavə edə bilməzsiniz."

#: account/adapter.py:76
msgid "A user is already registered with this phone number."
msgstr "Bu telefon nömrəsi ilə artıq bir istifadəçi qeydiyyatdan keçib."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Həddindən artıq uğursuz giriş cəhdi. Biraz sonra yenidən cəhd edin."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "Bu e-poçt ünvanı heç bir istifadəçi hesabına aid deyil."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Bu telefon nömrəsi heç bir istifadəçi hesabına aid deyil."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Əsas e-poçt ünvanınız təsdiqlənməlidir."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"İstifadəçi adı istifadə edilə bilməz. Zəhmət olmasa başqa istifadəçi adından "
"istifadə edin."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Göstərdiyiniz istifadəçi adı və/yaxud şifrə düzgün deyil."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Zəhmət olmasa, yalnız birini seçin."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Yeni dəyər cari dəyərdən fərqli olmalıdır."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Şifrənizi istifadə edin"

#: account/adapter.py:787
#, fuzzy
#| msgid "Use your authenticator app"
msgid "Use authenticator app or code"
msgstr "Autentifikator tətbiqindən istifadə edin"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
#, fuzzy
#| msgid "secret key"
msgid "Use a security key"
msgstr "gizli açar"

#: account/admin.py:23
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Mark selected email addresses as verified"
msgstr "Əsas e-poçt ünvanınız təsdiqlənməlidir."

#: account/apps.py:11
msgid "Accounts"
msgstr "Hesablar"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Hər dəfə eyni şifrəni daxil etməlisiniz."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Şifrə"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Məni xatırla"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "E-poçt ünvanı"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-poçt"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "İstifadəçi adı"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Daxil ol"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "İstifadəçi adı və ya e-poçt"

#: account/forms.py:156
msgid "Username or email"
msgstr "İstifadəçi adı və ya e-poçt"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "İstifadəçi adı və ya e-poçt"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "E-poçt (istəyə bağlı)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Şifrəni unutmusan?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-poçt (təkrar)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "E-poçt ünvanı təsdiqi"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-poçt (istəyə bağlı)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "E-poçt (istəyə bağlı)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Hər dəfə eyni e-poçtu daxil etməlisiniz."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Şifrə (təkrar)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Mövcud şifrə"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Yeni şifrə"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Yeni şifrə (təkrar)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Kod"

#: account/models.py:26
msgid "user"
msgstr "istifadəçi"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "e-poçt ünvanı"

#: account/models.py:34
msgid "verified"
msgstr "doğrulanmış"

#: account/models.py:35
msgid "primary"
msgstr "əsas"

#: account/models.py:41
msgid "email addresses"
msgstr "e-poçt ünvanları"

#: account/models.py:151
msgid "created"
msgstr "yaradılmış"

#: account/models.py:152
msgid "sent"
msgstr "göndərildi"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "açar"

#: account/models.py:158
msgid "email confirmation"
msgstr "e-poçt təsdiqi"

#: account/models.py:159
msgid "email confirmations"
msgstr "e-poçt təsdiqləri"

#: headless/apps.py:7
msgid "Headless"
msgstr ""

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Siz iki faktorlu doğrulama ilə qorunan hesaba e-poçt ünvanı əlavə edə "
"bilməzsiniz."

#: mfa/adapter.py:35
#, fuzzy
#| msgid ""
#| "You cannot add an email address to an account protected by two-factor "
#| "authentication."
msgid "You cannot deactivate two-factor authentication."
msgstr ""
"Siz iki faktorlu doğrulama ilə qorunan hesaba e-poçt ünvanı əlavə edə "
"bilməzsiniz."

#: mfa/adapter.py:38
#, fuzzy
#| msgid ""
#| "You cannot add an email address to an account protected by two-factor "
#| "authentication."
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Siz iki faktorlu doğrulama ilə qorunan hesaba e-poçt ünvanı əlavə edə "
"bilməzsiniz."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Siz e-poçt ünvanınızı doğrulamayana qədər iki faktorlu doğrulamanı "
"aktivləşdirə bilməzsiniz."

#: mfa/adapter.py:141
#, fuzzy
#| msgid "secret key"
msgid "Master key"
msgstr "gizli açar"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr ""

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr ""

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Bərpa kodları"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP Autentifikator"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr ""

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Doğrulama kodu"

#: mfa/webauthn/forms.py:59
#, fuzzy
#| msgid "Password"
msgid "Passwordless"
msgstr "Şifrə"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Bu e-poçt ünvanı ilə artıq hesab mövcuddur. Lütfən, əvvəlcə həmin hesaba "
"daxil olun, sonra %s hesabınızı birləşdirin."

#: socialaccount/adapter.py:39
#, fuzzy
#| msgid "Bad Token"
msgid "Invalid token."
msgstr "Yanlış Token"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Hesabınızda şifrə quraşdırılmayıb."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Hesabınızın təsdiqlənmiş e-poçt ünvanı yoxdur."

#: socialaccount/adapter.py:43
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third-party "
#| "accounts:"
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Aşağıdakı üçüncü tərəf hesablarından hər hansı birini istifadə edərək "
"hesabınıza daxil ola bilərsiniz:"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "Sosial hesab artıq başqa hesaba qoşulub."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Sosial Hesablar"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "provayder"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "provayder ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "ad"

#: socialaccount/models.py:58
msgid "client id"
msgstr "müştəri id"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "Tətbiq ID-si və ya istehlakçı açarı"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "gizli açar"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API sirri, müştəri sirri və ya istehlakçı sirri"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Açar"

#: socialaccount/models.py:81
msgid "social application"
msgstr "sosial tətbiq"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "sosial tətbiqlər"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "son giriş"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "qoşulma tarixi"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "əlavə məlumat"

#: socialaccount/models.py:125
msgid "social account"
msgstr "sosial hesab"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "sosial hesablar"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) və ya giriş tokeni (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token sirri"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) və ya token yeniləyin (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "vaxtı bitir"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "sosial tətbiq tokeni"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "sosial tətbiq tokenləri"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Yanlış profil məlumatı"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
#, fuzzy
#| msgctxt "field label"
#| msgid "Login"
msgid "Login"
msgstr "Daxil ol"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Ləğv et"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"\"%s\"-dən sorğu tokeni alınarkən yanlış cavab alındı. Cavab belə oldu: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "\"%s\"-dən giriş tokeni əldə edərkən yanlış cavab alındı."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "\"%s\" üçün heç bir sorğu tokeni saxlanılmadı."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "\"%s\" üçün heç bir giriş tokeni saxlanılmadı."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "\"%s\" ünvanında şəxsi resurslara giriş yoxdur."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "\"%s\"-dən sorğu tokeni alınarkən uğursuz cavab alındı."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Hesab Aktiv Deyil"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Bu hesab aktiv deyil."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Təsdiq edin"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Recovery Codes"
msgid "Request new code"
msgstr "Bərpa kodları"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Girişi Təsdiqlə"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Hesabınızı qorumaq üçün lütfən, yenidən doğrulayın."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Alternativ variantlar"

#: templates/account/confirm_email_verification_code.html:5
#, fuzzy
#| msgid "email confirmation"
msgid "Email Verification"
msgstr "e-poçt təsdiqi"

#: templates/account/confirm_email_verification_code.html:8
#, fuzzy
#| msgid "Enter an authenticator code:"
msgid "Enter Email Verification Code"
msgstr "Autentifikator kodunu daxil edin:"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "e-poçt ünvanı"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Daxil Ol"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr ""

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Şifrə Sıfırlama"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Şifrə Sıfırlama"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Doğrulamanı Yenidən Göndər"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter an authenticator code:"
msgid "Enter Phone Verification Code"
msgstr "Autentifikator kodunu daxil edin:"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-poçt Ünvanları"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Aşağıdakı e-poçt ünvanları hesabınızla əlaqələndirilir:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Doğrulanmış"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Doğrulanmamış"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Əsas"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Əsas Et"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Doğrulamanı Yenidən Göndər"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Sil"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "E-poçt Ünvanı Əlavə Et"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "E-poçt Əlavə Et"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Həqiqətən seçilmiş e-poçt ünvanını silmək istəyirsiniz?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Siz və ya başqası e-poçt ünvanından istifadə edərək\n"
"hesab üçün qeydiyyatdan keçməyə çalışdığınız üçün bu e-məktubu alırsınız:\n"
"\n"
"%(email)s\n"
"\n"
"Lakin həmin e-poçt ünvanından istifadə edən hesab artıq mövcuddur. Bunu "
"unutmusunuzsa, lütfən, hesabınızı bərpa etmək üçün unudulmuş şifrə "
"prosedurundan istifadə edin:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Hesab Artıq Mövcuddur"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "%(site_name)s-dan Salam!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"%(site_name)s!\n"
"%(site_domain)s istifadə etdiyiniz üçün təşəkkür edirik"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Cancel"
msgid "Email Changed"
msgstr "Ləğv et"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "Your email has been confirmed."
msgstr "Siz %(email)s-u təsdiqlədiniz."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "e-poçt təsdiqi"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this email because user %(user_display)s has given your "
#| "email address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"%(user_display)s istifadəçisi %(site_domain)s-də hesabı qeydiyyatdan "
"keçirmək üçün e-poçt ünvanınızı verdiyi üçün bu e-məktubu alırsınız.\n"
"\n"
"Bunun düzgün olduğunu təsdiqləmək üçün %(activate_url)s linkinə keçid edin."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Lütfən e-poçt ünvanınızı təsdiqləyin"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "Sil"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""

#: templates/account/email/login_code_subject.txt:3
#, fuzzy
#| msgid "Sign In"
msgid "Sign-In Code"
msgstr "Daxil Ol"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "Şifrəniz dəyiştirildi."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "Şifrə (təkrar)"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Şifrə Sıfırlama"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Siz və ya başqa biri hesabınız üçün şifrə sıfırlamasını tələb etdiyinə görə "
"bu e-məktubu alırsınız.\n"
"Əgər belə bir sorğu etməmisinizsə, bu e-məktubu gözardı edə bilərsiniz. "
"Şifrənizi sıfırlamaq üçün aşağıdakı linkə keçid edin."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Əgər unutmusunuzsa, istifadəçi adınız %(username)s-dir."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Şifrə Sıfırlama E-poçtu"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been reset."
msgstr "Şifrəniz dəyiştirildi."

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been set."
msgstr "Şifrəniz dəyiştirildi."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "Şifrə Sıfırlama"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""

#: templates/account/email/unknown_account_subject.txt:3
#, fuzzy
#| msgid "Accounts"
msgid "Unknown Account"
msgstr "Hesablar"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-poçt Ünvanı"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
#, fuzzy
#| msgid "Current Password"
msgid "Current email"
msgstr "Mövcud şifrə"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr ""

#: templates/account/email_change.html:35
#, fuzzy
#| msgid "Your email address is still pending verification:"
msgid "Your email address is still pending verification."
msgstr "E-poçt ünvanınız hələ də doğrulamanı gözləyir:"

#: templates/account/email_change.html:41
#, fuzzy
#| msgid "Cancel"
msgid "Cancel Change"
msgstr "Ləğv et"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
#, fuzzy
#| msgid "Change Email"
msgid "Change to"
msgstr "E-poçt Dəyiş"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "E-poçt Dəyiş"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "E-poçt Ünvanını Təsdiqlə"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Lütfən, təsdiq edin ki, <a href=\"mailto:%(email)s\">%(email)s</a> "
"%(user_display)s istifadəçisi üçün e-poçt ünvanıdır."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"%(email)s-u təsdiqləmək mümkün deyil, çünki, o, artıq başqa hesab tərəfindən "
"təsdiqlənib."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Bu e-poçt təsdiqi linkinin vaxtı keçib və ya etibarsızdır. Lütfən, <a "
"href=\"%(email_url)s\">yeni e-poçt təsdiq sorğusu göndərin</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Hələ hesab yaratmamısınızsa, lütfən, əvvəlcə %(link)sqeydiyyatdan "
"keçin%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr ""

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr ""

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Çıxış"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Hesabdan çıxmaq istədiyinizə əminsiniz?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Siz əsas e-poçt ünvanınızı (%(email)s) silə bilməzsiniz."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Təsdiq e-məktubu %(email)s ünvanına göndərildi."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Siz %(email)s-u təsdiqlədiniz."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "%(email)s e-poçt ünvanı silindi."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "%(name)s olaraq uğurla daxil oldun."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Siz hesabdan çıxdınız."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr ""

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Şifrə uğurla dəyişdirildi."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Şifrə uğurla təyin edildi."

#: templates/account/messages/phone_verification_sent.txt:2
#, python-format
msgid "A verification code has been sent to %(phone)s."
msgstr ""

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Əsas e-poçt ünvanı təyin edildi."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Şifrəni Dəyiş"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Şifrəni Unutmusunuz?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Şifrənizi unutmusunuz? Aşağıya e-poçt ünvanınızı daxil edin və biz sizə onu "
"sıfırlamağa imkan verən e-məktub göndərəcəyik."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Şifrəmi Sıfırlayın"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Şifrənizi sıfırlamaqla bağlı hər hansı probleminiz olarsa, bizimlə əlaqə "
"saxlayın."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Sizə e-məktub göndərdik. Əgər onu almamısınızsa, spam qovluğunuzu yoxlayın. "
"Əks halda, bir neçə dəqiqə ərzində onu almasanız, bizimlə əlaqə saxlayın."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Yanlış Token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Şifrə sıfırlama linki etibarsız idi, ola bilsin ki, artıq istifadə olunub. "
"Lütfən, <a href=\"%(passwd_reset_url)s\">yeni şifrə sıfırlamasını</a> tələb "
"edin."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Şifrəniz dəyiştirildi."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Şifrə Təyin Et"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change Email"
msgid "Change Phone"
msgstr "E-poçt Dəyiş"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current Password"
msgid "Current phone"
msgstr "Mövcud şifrə"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification:"
msgid "Your phone number is still pending verification."
msgstr "E-poçt ünvanınız hələ də doğrulamanı gözləyir:"

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Şifrənizi daxil edin:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr ""

#: templates/account/request_login_code.html:24
#, fuzzy
#| msgid "Recovery Codes"
msgid "Request Code"
msgstr "Bərpa kodları"

#: templates/account/request_login_code.html:30
#, fuzzy
#| msgid "Alternative options"
msgid "Other sign-in options"
msgstr "Alternativ variantlar"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Qeydiyyatdan keçin"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Qeydiyyatdan Keç"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Artıq bir hesabınız var? Lütfən %(link)sdaxil olun%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr ""

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "Qeydiyyatdan Keç"

#: templates/account/signup_by_passkey.html:36
#, fuzzy
#| msgid "Alternative options"
msgid "Other options"
msgstr "Alternativ variantlar"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Qeydiyyat Bağlıdır"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Üzr istəyirik, lakin qeydiyyat hazırda bağlıdır."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Qeyd"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Siz artıq %(user_display)s kimi daxil olmusunuz."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Xəbərdarlıq:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Hazırda ayarlanmış e-poçt ünvanınız yoxdur. Siz mütləq e-poçt ünvanı əlavə "
"etməlisiniz ki, bildirişlər ala, şifrənizi sıfırlayasınız və s."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "E-poçt Ünvanınızı Doğrulayın"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Doğrulama üçün sizə e-məktub göndərdik. Qeydiyyat prosesini yekunlaşdırmaq "
"üçün verilən linki izləyin. Doğrulama e-məktubunu əsas gələnlər qutunuzda "
"görmürsünüzsə, spam qovluğunuzu yoxlayın. Bir neçə dəqiqə ərzində doğrulama "
"e-məktubunu almasanız, bizimlə əlaqə saxlayın."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Saytın bu hissəsi siz olduğunuzu təsdiq etməyimizi tələb edir\n"
".Bu məqsədlə sizdən e-poçt ünvanınızın sahibliyini\n"
"doğrulamanızı tələb edirik."

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Doğrulama üçün\n"
"sizə e-məktub göndərdik. Zəhmət olmasa həmin e-poçtun içindəki linkə keçid "
"edin. Doğrulama e-poçtunu əsas gələnlər qutunuzda görmürsünüzsə, spam "
"qovluğunuzu yoxlayın. Əks halda\n"
"bir neçə dəqiqə ərzində onu almasanız, bizimlə əlaqə saxlayın."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Qeyd:</strong> siz hələ də <a href=\"%(email_url)s\">e-poçt "
"ünvanınızı dəyişə bilərsiniz</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Mesajlar:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menyu:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Hesab Əlaqələri"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "İki Faktorlu Doğrulama"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Hesabınız iki faktorlu doğrulama ilə qorunur. Zəhmət olmasa autentifikator "
"kodunu daxil edin:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
#, fuzzy
#| msgid "A new set of recovery codes has been generated."
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "Yeni bərpa kodları dəsti yaradıldı."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
#, fuzzy
#| msgid "Recovery Codes"
msgid "New Recovery Codes Generated"
msgstr "Bərpa kodları"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Autentifikator tətbiqi aktivləşdirildi."

#: templates/mfa/email/totp_activated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app activated."
msgid "Authenticator App Activated"
msgstr "Autentifikator tətbiqi aktivləşdirildi."

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Autentifikator tətbiqi deaktiv edildi."

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app deactivated."
msgid "Authenticator App Deactivated"
msgstr "Autentifikator tətbiqi deaktiv edildi."

#: templates/mfa/email/webauthn_added_message.txt:4
#, fuzzy
#| msgid "A new set of recovery codes has been generated."
msgid "A new security key has been added."
msgstr "Yeni bərpa kodları dəsti yaradıldı."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr ""

#: templates/mfa/email/webauthn_removed_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "A security key has been removed."
msgstr "Siz %(email)s-u təsdiqlədiniz."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Autentifikator Tətbiqi"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Autentifikator tətbiqindən istifadə edərək doğrulama aktivdir."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Autentifikator tətbiqi aktiv deyil."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deaktiv et"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Aktivləşdir"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr ""

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr ""

#: templates/mfa/index.html:62
msgid "Manage"
msgstr ""

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr ""

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Bərpa kodları"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "Əlçatan %(total_count)s bərpa kodundan %(unused_count)s var."
msgstr[1] "Əlçatan %(total_count)s bərpa kodundan %(unused_count)s var."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Bərpa kodları qurulmayıb."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Bax"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Yüklə"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Yarat"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Yeni bərpa kodları dəsti yaradıldı."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr ""

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr ""

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Autentifikator kodunu daxil edin:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Hesabınız üçün yeni bərpa kodları dəsti yaratmaq üzrəsiniz."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Bu əməliyyat mövcud kodlarınızı etibarsız edəcək."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Siz əminsiniz?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "İstifadə edilməmiş kodlar"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Kodları yükləyin"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Yeni kodlar yarat"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Autentifikator Tətbiqini Aktivləşdirin"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Hesabınızı iki faktorlu doğrulama ilə qorumaq üçün autentifikator "
"tətbiqinizlə aşağıdakı QR kodunu skan edin. Sonra, aşağıdakı proqram "
"tərəfindən yaradılan doğrulama kodunu daxil edin."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Autentifikator sirri"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Siz bu sirri saxlaya və ondan autentifikator tətbiqinizi daha sonra yenidən "
"quraşdırmaq üçün istifadə edə bilərsiniz."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Autentifikator Tətbiqini Deaktiv Et"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Siz autentifikator tətbiqinə əsaslanan doğrulamanı deaktiv etmək üzrəsiniz. "
"Siz əminsiniz?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Add Security Key"
msgstr "gizli açar"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
#, fuzzy
#| msgid "secret key"
msgid "Remove Security Key"
msgstr "gizli açar"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
#, fuzzy
#| msgid "Are you sure you want to sign out?"
msgid "Are you sure you want to remove this security key?"
msgstr "Hesabdan çıxmaq istədiyinizə əminsiniz?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:37
#, fuzzy
#| msgid "secret key"
msgid "Security key"
msgstr "gizli açar"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:41
#, fuzzy
#| msgid "Unverified"
msgid "Unspecified"
msgstr "Doğrulanmamış"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Edit Security Key"
msgstr "gizli açar"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr ""

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Current Password"
msgid "Create Passkey"
msgstr "Mövcud şifrə"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "yaradılmış"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "Sosial Şəbəkəyə Giriş Xətası"

#: templates/socialaccount/authentication_error.html:12
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Sosial şəbəkə hesabınız vasitəsilə daxil olmağa cəhd edərkən xəta baş verdi."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Aşağıdakı üçüncü tərəf hesablarından hər hansı birini istifadə edərək "
"hesabınıza daxil ola bilərsiniz:"

#: templates/socialaccount/connections.html:46
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "Hazırda bu hesaba qoşulmuş heç bir sosial şəbəkə hesabınız yoxdur."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Üçüncü Tərəf Hesabı Əlavə Et"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Connected"
msgstr "Üçüncü Tərəf Hesabı Əlavə Et"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Disconnected"
msgstr "Üçüncü Tərəf Hesabı Əlavə Et"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "%(provider)s ilə əlaqə"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Siz %(provider)s-dən yeni üçüncü tərəf hesabını qoşmaq üzrəsiniz."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "%(provider)s vasitəsi ilə daxil olun"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Siz %(provider)s-dən üçüncü tərəf hesabından istifadə etməklə daxil olmaq "
"üzrəsiniz."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Davam et"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Giriş Ləğv Edildi"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Mövcud hesablarınızdan birini istifadə edərək saytımıza daxil olmağı ləğv "
"etmək qərarına gəldiniz. Bu səhvdirsə, <a href=\"%(login_url)s\">daxil olun</"
"a>."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "Sosial hesab bağlandı."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "Sosial hesabın əlaqəsi kəsilib."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Siz \n"
" %(site_name)s hesabına daxil olmaq üçün %(provider_name)s hesabınızdan "
"istifadə etmək üzrəsiniz. Son addım olaraq, aşağıdakı formu doldurun:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Və ya üçüncü tərəf istifadə edin"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Address"
msgid "IP Address"
msgstr "E-poçt Ünvanı"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
#, fuzzy
#| msgid "Current Password"
msgid "Current"
msgstr "Mövcud şifrə"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:92
msgid "session key"
msgstr ""

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Hesab Əlaqələri"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Şifrə ən az {0} simvoldan ibarət olmalıdır."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Sizin və ya başqa birinin istifadəçi hesabınız üçün\n"
#~ "şifrə tələb etdiyini bildirmək üçün bu e-məktubu alırsınız. Lakin, bizim "
#~ "verilənlər bazamızda\n"
#~ "%(email)s e-poçt ünvanına sahib heç bir istifadəçi məlumatı yoxdur.\n"
#~ "\n"
#~ "Şifrənin sıfırlanmasını tələb etməmisinizsə, bu e-məktubu gözardı edə "
#~ "bilərsiniz.\n"
#~ "\n"
#~ "Əgər bu siz idinizsə, siz aşağıdakı linkdən istifadə edərək hesaba daxil "
#~ "ola bilərsiniz."

#~ msgid "The following email address is associated with your account:"
#~ msgstr "Aşağıdakı e-poçt ünvanı hesabınızla əlaqələndirilib:"

#~ msgid "Change Email Address"
#~ msgstr "E-poçt Ünvanını Dəyiş"
