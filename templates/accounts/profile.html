{% extends 'base.html' %}

{% block title %}Profile - YalaOffice{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user"></i> My Profile</h1>
            <a href="{% url 'accounts:dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <!-- Profile Summary -->
        <div class="card">
            <div class="card-body text-center">
                {% if profile.avatar %}
                    <img src="{{ profile.avatar.url }}" alt="Avatar" class="rounded-circle mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                {% else %}
                    <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                        <i class="fas fa-user fa-3x text-white"></i>
                    </div>
                {% endif %}
                
                <h5>{{ user.get_full_name|default:user.username }}</h5>
                <p class="text-muted">{{ user.email }}</p>
                <span class="badge bg-primary">{{ user.get_role_display }}</span>
                
                {% if user.role == 'reseller' and profile.company_name %}
                    <div class="mt-3">
                        <h6><i class="fas fa-building"></i> {{ profile.company_name }}</h6>
                        {% if profile.tax_id %}
                            <small class="text-muted">Tax ID: {{ profile.tax_id }}</small>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Account Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> Account Information</h6>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-sm-6"><strong>Member Since:</strong></div>
                    <div class="col-sm-6">{{ user.date_joined|date:"M d, Y" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-6"><strong>Last Login:</strong></div>
                    <div class="col-sm-6">{{ user.last_login|date:"M d, Y H:i"|default:"Never" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-6"><strong>Status:</strong></div>
                    <div class="col-sm-6">
                        {% if user.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <!-- Profile Form -->
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-edit"></i> Edit Profile</h6>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- Personal Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="id_first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="id_first_name" name="first_name" value="{{ user.first_name }}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="id_last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="id_last_name" name="last_name" value="{{ user.last_name }}" readonly>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="id_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="id_email" name="email" value="{{ user.email }}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="id_phone" class="form-label">Phone</label>
                            <input type="text" class="form-control" id="id_phone" name="phone" value="{{ user.phone }}" readonly>
                        </div>
                    </div>
                    
                    <!-- Profile Fields -->
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger">{{ form.address.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.city.id_for_label }}" class="form-label">City</label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="text-danger">{{ form.city.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.postal_code.id_for_label }}" class="form-label">Postal Code</label>
                            {{ form.postal_code }}
                            {% if form.postal_code.errors %}
                                <div class="text-danger">{{ form.postal_code.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.preferred_branch.id_for_label }}" class="form-label">Preferred Branch</label>
                            {{ form.preferred_branch }}
                            {% if form.preferred_branch.errors %}
                                <div class="text-danger">{{ form.preferred_branch.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">Date of Birth</label>
                            {{ form.date_of_birth }}
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger">{{ form.date_of_birth.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.avatar.id_for_label }}" class="form-label">Profile Picture</label>
                        {{ form.avatar }}
                        {% if form.avatar.errors %}
                            <div class="text-danger">{{ form.avatar.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Delivery Location -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6><i class="fas fa-map-marker-alt"></i> Delivery Location</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="{{ form.delivery_latitude.id_for_label }}" class="form-label">Latitude</label>
                                    {{ form.delivery_latitude }}
                                    {% if form.delivery_latitude.errors %}
                                        <div class="text-danger">{{ form.delivery_latitude.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.delivery_longitude.id_for_label }}" class="form-label">Longitude</label>
                                    {{ form.delivery_longitude }}
                                    {% if form.delivery_longitude.errors %}
                                        <div class="text-danger">{{ form.delivery_longitude.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <small class="text-muted">You can set your delivery location coordinates for more accurate deliveries.</small>
                        </div>
                    </div>
                    
                    <!-- Company Information (for resellers) -->
                    {% if user.role == 'reseller' %}
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6><i class="fas fa-building"></i> Company Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="{{ form.company_name.id_for_label }}" class="form-label">Company Name</label>
                                    {{ form.company_name }}
                                    {% if form.company_name.errors %}
                                        <div class="text-danger">{{ form.company_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.tax_id.id_for_label }}" class="form-label">Tax ID</label>
                                    {{ form.tax_id }}
                                    {% if form.tax_id.errors %}
                                        <div class="text-danger">{{ form.tax_id.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'accounts:dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
