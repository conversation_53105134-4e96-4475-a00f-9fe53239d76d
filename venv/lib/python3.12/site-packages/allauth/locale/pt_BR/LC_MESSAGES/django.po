# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2013-2014
# <PERSON> <<EMAIL>>, 2013
# <PERSON> <<EMAIL>>, 2013-2014
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-08-30 07:32+0000\n"
"Last-Translator: Al<PERSON><PERSON><PERSON> Ribeiro Nascimento <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) <https://hosted.weblate.org/projects/"
"allauth/django-allauth/pt_BR/>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.7.1-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Esta conta está atualmente inativa."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Você não pode remover seu endereço de e-mail principal."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Este endereço de e-mail já está associado a esta conta."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "O endereço de e-mail e/ou senha especificados estão incorretos."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "O nome de usuário e/ou senha especificados estão incorretos."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Um usuário já está registrado com este endereço de e-mail."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Por favor, digite sua senha atual."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Código incorreto."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Senha incorreta."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Chave inválida ou expirada."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "Token inválido."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "O token de redefinição de senha era inválido."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Você não pode adicionar mais de %d endereços de e-mail."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Um usuário já está registrado com este endereço de e-mail."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Muitas tentativas de login falharam. Tente novamente mais tarde."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "O endereço de e-mail não está atribuído a nenhuma conta de usuário"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "O endereço de e-mail não está atribuído a nenhuma conta de usuário"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Seu endereço de e-mail principal deve ser verificado."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Nome de usuário não pode ser utilizado. Por favor, use outro nome de usuário."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "O nome de usuário e/ou senha especificados estão incorretos."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Use sua senha"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Use um aplicativo autenticador ou código"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Use uma chave de segurança"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Marcar os endereços de e-mail selecionados como verificados"

#: account/apps.py:11
msgid "Accounts"
msgstr "Contas"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Você deve digitar a mesma senha todas as vezes."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Senha"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Lembrar de Mim"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Endereço de e-mail"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Nome de usuário"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Login"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Nome de usuário ou e-mail"

#: account/forms.py:156
msgid "Username or email"
msgstr "Nome de usuário ou e-mail"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Nome de usuário ou e-mail"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "E-mail (opcional)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Esqueceu sua senha?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-mail (novamente)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Confirmação de endereço de e-mail"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-mail (opcional)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "E-mail (opcional)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Você deve digitar o mesmo e-mail todas as vezes."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Senha (novamente)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Senha atual"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nova senha"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nova senha (novamente)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Código"

#: account/models.py:26
msgid "user"
msgstr "usuário"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "endereço de e-mail"

#: account/models.py:34
msgid "verified"
msgstr "verificado"

#: account/models.py:35
msgid "primary"
msgstr "principal"

#: account/models.py:41
msgid "email addresses"
msgstr "endereços de e-mail"

#: account/models.py:151
msgid "created"
msgstr "criado"

#: account/models.py:152
msgid "sent"
msgstr "enviado"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "chave"

#: account/models.py:158
msgid "email confirmation"
msgstr "confirmação de e-mail"

#: account/models.py:159
msgid "email confirmations"
msgstr "confirmações de e-mail"

#: headless/apps.py:7
msgid "Headless"
msgstr "Headless"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Você não pode adicionar um endereço de e-mail a uma conta protegida por "
"autenticação de dois fatores."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Você não pode desativar a autenticação de dois fatores."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Você não pode gerar códigos de recuperação sem ter a autenticação de dois "
"fatores ativada."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Você não pode ativar a autenticação de dois fatores até que tenha verificado "
"seu endereço de e-mail."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Chave mestre"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Chave de backup"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Chave nº {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Códigos de recuperação"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Autenticador TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Código do autenticador"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Sem senha"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Ativar a operação sem senha permite que você faça login apenas com esta "
"chave, mas impõe requisitos adicionais, como biometria ou proteção com PIN."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Uma conta já existe com este endereço de e-mail. Faça login nessa conta "
"primeiro e depois conecte sua conta %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Token inválido."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Sua conta não tem senha configurada."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Sua conta não tem um endereço de e-mail verificado."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Você não pode desconectar sua última conta de terceiros conectada."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "A conta de terceiros já está conectada a uma conta diferente."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Contas sociais"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "provedor"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID do provedor"

#: socialaccount/models.py:56
msgid "name"
msgstr "nome"

#: socialaccount/models.py:58
msgid "client id"
msgstr "ID do cliente"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "ID do aplicativo ou chave do consumidor"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "chave secreta"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "Segredo da API, segredo do cliente ou segredo do consumidor"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Chave"

#: socialaccount/models.py:81
msgid "social application"
msgstr "aplicativo social"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "aplicativos sociais"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "último login"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "data de entrada"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "dados adicionais"

#: socialaccount/models.py:125
msgid "social account"
msgstr "conta social"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "contas sociais"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ou token de acesso (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "segredo do token"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ou token de atualização (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "expira em"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token de aplicativo social"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "tokens de aplicativos sociais"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Dados de perfil inválidos"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Login"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Cancelar"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Resposta inválida ao obter o token de solicitação de \"%s\". A resposta foi: "
"%s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Resposta inválida ao obter o token de acesso de \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nenhum token de solicitação salvo para \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nenhum token de acesso salvo para \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Sem acesso a recursos privados em \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Resposta inválida ao obter o token de solicitação de \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Conta inativa"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Esta conta está inativa."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Enviamos um código para %(email_link)s. O código expira em breve, então "
"insira-o logo."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Confirmar"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Solicitar código"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Confirmar acesso"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Por favor, reautentique para proteger sua conta."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Opções alternativas"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Verificação de e-mail"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Insira o código de verificação de e-mail"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "endereço de e-mail"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Entrar"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Insira o código de entrada"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Redefinição de senha"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Redefinição de senha"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Reenviar verificação"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "Insira o código de verificação de e-mail"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Endereços de e-mail"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Os seguintes endereços de e-mail estão associados à sua conta:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Verificado"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Não verificado"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Principal"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Tornar principal"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Reenviar verificação"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Remover"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Adicionar endereço de e-mail"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Adicionar e-mail"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Você realmente deseja remover o endereço de e-mail selecionado?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Você está recebendo este e-mail porque você ou outra pessoa tentou se "
"inscrever para\n"
"uma conta usando o endereço de e-mail:\n"
"\n"
"%(email)s\n"
"\n"
"No entanto, uma conta com este endereço de e-mail já existe. Caso você "
"tenha\n"
"esquecido disso, por favor use o procedimento de recuperação de senha para\n"
"recuperar sua conta:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Conta já existente"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Olá da %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Obrigado por usar %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Você está recebendo este e-mail porque a seguinte alteração foi feita em sua "
"conta:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Se você não reconhece essa alteração, por favor tome as devidas precauções "
"de segurança imediatamente. A alteração em sua conta se origina de:\n"
"\n"
"- Endereço IP: %(ip)s\n"
"- Navegador: %(user_agent)s\n"
"- Data: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Seu e-mail foi alterado de %(from_email)s para %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "E-mail alterado"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Seu e-mail foi confirmado."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Confirmação de e-mail"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Você está recebendo este e-mail porque o usuário %(user_display)s forneceu "
"seu\n"
"endereço de e-mail para registrar uma conta em %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Seu código de verificação de e-mail está listado abaixo. Por favor, insira-o "
"na janela do seu navegador aberta."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Para confirmar que isso está correto, acesse %(activate_url)s."

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Por favor, confirme seu endereço de e-mail."

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "O endereço de e-mail %(deleted_email)s foi removido de sua conta."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "E-mail removido"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Seu código de entrada está listado abaixo. Por favor, insira-o na janela do "
"seu navegador aberta."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Este e-mail pode ser ignorado com segurança se você não iniciou esta ação."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Código de entrada"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Sua senha foi alterada."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Senha alterada"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Seu código de entrada está listado abaixo. Por favor, insira-o na janela do "
"seu navegador aberta."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Redefinição de senha"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Você está recebendo este e-mail porque você ou outra pessoa solicitou a "
"redefinição de senha para sua conta de usuário.\n"
"Este e-mail pode ser ignorado com segurança se você não solicitou a "
"redefinição de senha. Clique no link abaixo para redefinir sua senha."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Caso tenha esquecido, seu nome de usuário é %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail de redefinição de senha"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Sua senha foi redefinida."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Sua senha foi definida."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Senha definida"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Você está recebendo este e-mail porque você, ou outra pessoa, tentou acessar "
"uma conta com o e-mail %(email)s. No entanto, não temos nenhum registro de "
"tal conta em nosso banco de dados."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "Se foi você, pode se inscrever para uma conta usando o link abaixo."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Conta desconhecida"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Endereço de e-mail"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "E-mail atual"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Alterando para"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Seu endereço de e-mail ainda está aguardando verificação."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Cancelar alteração"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Alterar para"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Alterar e-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Confirmar endereço de e-mail"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Por favor, confirme que <a href=\"mailto:%(email)s\">%(email)s</a> é um "
"endereço de e-mail para o usuário %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Incapaz de confirmar %(email)s porque já foi confirmado por uma conta "
"diferente."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Este link de confirmação de e-mail expirou ou é inválido. Por favor, <a "
"href=\"%(email_url)s\">emita uma nova solicitação de confirmação de e-mail</"
"a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Se você ainda não criou uma conta, por favor, %(link)scadastre-"
"se%(end_link)s primeiro."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Entrar com uma chave de segurança"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "Envie-me um código de entrada por e-mail."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Sair"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Tem certeza de que deseja sair?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Você não pode remover seu endereço de e-mail principal (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "E-mail de confirmação enviado para %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Você confirmou %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Endereço de e-mail removido %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Conectado com sucesso como %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Você saiu."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Um código de entrada foi enviado para %(email)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Senha alterada com sucesso."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Senha definida com sucesso."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Um código de entrada foi enviado para %(email)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Endereço de e-mail principal definido."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Alterar senha"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Esqueceu a senha?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Esqueceu sua senha? Insira seu endereço de e-mail abaixo e nós enviaremos um "
"e-mail permitindo que você a redefina."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Redefinir minha senha"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Por favor, entre em contato conosco se tiver algum problema ao redefinir sua "
"senha."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Enviamos um e-mail para você. Se você não o recebeu, por favor, verifique "
"sua pasta de spam. Caso contrário, entre em contato conosco se não o receber "
"em alguns minutos."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Token inválido"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"O link de redefinição de senha era inválido, possivelmente porque já foi "
"utilizado. Por favor, solicite uma <a href=\"%(passwd_reset_url)s\">nova "
"redefinição de senha</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Sua senha foi alterada."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Definir senha"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "Alterar para"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "Atual"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "Seu endereço de e-mail ainda está aguardando verificação."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Insira sua senha:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr ""
"Você receberá um e-mail contendo um código especial para uma entrada sem "
"senha."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Solicitar código"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Outras opções de entrada"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Cadastrar-se"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Cadastrar-se"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Já tem uma conta? Então, por favor, %(link)ssign in%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Cadastrar-se usando uma chave de segurança"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Cadastro com chave de segurança"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Outras opções"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Cadastro fechado"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Desculpe, mas o cadastro está atualmente fechado."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Nota"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Você já está conectado como %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Aviso:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Atualmente, você não tem nenhum endereço de e-mail configurado. Você deve "
"realmente adicionar um endereço de e-mail para que possa receber "
"notificações, redefinir sua senha, etc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Verifique seu endereço de e-mail"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Enviamos um e-mail para você para verificação. Siga o link fornecido para "
"finalizar o processo de cadastro. Se você não encontrar o e-mail de "
"verificação em sua caixa de entrada principal, verifique sua pasta de spam. "
"Entre em contato conosco se não receber o e-mail de verificação dentro de "
"alguns minutos."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Esta parte do site exige que verifiquemos que você é quem afirma ser. Para "
"esse fim, exigimos que você\n"
"verifique a propriedade do seu endereço de e-mail. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Enviamos um e-mail para você para verificação. Por favor, clique no link "
"dentro desse e-mail. Se você não encontrar o e-mail de verificação em sua "
"caixa de entrada principal, verifique sua pasta de spam. Caso contrário, "
"entre em contato conosco se não o receber dentro de alguns minutos."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Nota:</strong> você ainda pode <a href=\"%(email_url)s\">alterar seu "
"endereço de e-mail</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Mensagens:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Conexões de conta"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Autenticação de dois fatores"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sessões"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Sua conta está protegida por autenticação de dois fatores. Por favor, insira "
"um código do autenticador:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Um novo conjunto de códigos de recuperação da autenticação de dois fatores "
"foi gerado."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Novos códigos de recuperação gerados"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Aplicativo autenticador ativado."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Aplicativo autenticador ativado"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Aplicativo autenticador desativado."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Aplicativo autenticador desativado"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Uma nova chave de segurança foi adicionada."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Chave de segurança adicionada"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Uma chave de segurança foi removida."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Chave de segurança removida"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Aplicativo autenticador"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "A autenticação usando um aplicativo autenticador está ativa."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Um aplicativo autenticador não está ativo."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Desativar"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Ativar"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Chaves de segurança"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Você adicionou %(count)s chave de segurança."
msgstr[1] "Você adicionou %(count)s chaves de segurança."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Nenhuma chave de segurança foi adicionada."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Gerenciar"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Adicionar"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Códigos de recuperação"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Há %(unused_count)s de %(total_count)s códigos de recuperação disponíveis."
msgstr[1] ""
"Há %(unused_count)s de %(total_count)s códigos de recuperação disponíveis."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Nenhum código de recuperação configurado."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Visualizar"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Baixar"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Gerar"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Um novo conjunto de códigos de recuperação foi gerado."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Chave de segurança adicionada."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Chave de segurança removida."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Insira um código do autenticador:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Você está prestes a gerar um novo conjunto de códigos de recuperação para "
"sua conta."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Esta ação invalidará seus códigos existentes."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Você tem certeza?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Códigos não utilizados"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Baixar códigos"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Gerar novos códigos"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Ativar aplicativo autenticador"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Para proteger sua conta com autenticação de dois fatores, escaneie o código "
"QR abaixo com seu aplicativo autenticador. Em seguida, insira o código de "
"verificação gerado pelo aplicativo abaixo."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Segredo do autenticador"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Você pode armazenar este segredo e usá-lo para reinstalar seu aplicativo "
"autenticador posteriormente."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Desativar aplicativo autenticador"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Você está prestes a desativar a autenticação baseada em aplicativo "
"autenticador. Você tem certeza?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Adicionar chave de segurança"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Remover chave de segurança"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Tem certeza de que deseja remover esta chave de segurança?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Uso"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Chave de segurança"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Chave de segurança"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Esta chave não indica se é uma chave de segurança."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Não especificado"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Adicionado em %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Último uso em %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Editar"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Editar chave de segurança"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Salvar"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Criar chave de segurança"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Você está prestes a criar uma chave de segurança para sua conta. Como você "
"pode adicionar chaves adicionais posteriormente, pode usar um nome "
"descritivo para diferenciar as chaves."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Criar"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Esta funcionalidade requer JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Falha no login de terceiros"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Ocorreu um erro ao tentar fazer login via sua conta de terceiros."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Você pode fazer login em sua conta usando qualquer uma das seguintes contas "
"de terceiros:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Atualmente, você não tem contas de terceiros conectadas a esta conta."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Adicionar uma conta de terceiros"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "Uma conta de terceiros de %(provider)s foi conectada à sua conta."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Conta de terceiros conectada"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "Uma conta de terceiros de %(provider)s foi desconectada de sua conta."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Conta de terceiros desconectada"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Conectar %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Você está prestes a conectar uma nova conta de terceiros de %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Entrar via %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Você está prestes a fazer login usando uma conta de terceiros de "
"%(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Continuar"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Login cancelado"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Você decidiu cancelar o login em nosso site usando uma de suas contas "
"existentes. Se isso foi um erro, por favor, continue para <a "
"href=\"%(login_url)s\">fazer login</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "A conta de terceiros foi conectada."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "A conta de terceiros foi desconectada."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Você está prestes a usar sua conta %(provider_name)s para fazer login em\n"
"%(site_name)s. Como passo final, por favor, complete o formulário a seguir:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Ou use uma conta de terceiros"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Saiu de todas as outras sessões."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Iniciado em"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "Endereço IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Navegador"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Última visualização em"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Atual"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Sair de outras sessões"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Sessões de usuário"

#: usersessions/models.py:92
msgid "session key"
msgstr "chave da sessão"
