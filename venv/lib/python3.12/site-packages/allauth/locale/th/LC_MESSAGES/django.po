# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <PERSON><PERSON>phoom Chaipreecha, 2015
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-09-11 08:09+0000\n"
"Last-Translator: Napapat Angsutrarux <<EMAIL>>\n"
"Language-Team: Thai <https://hosted.weblate.org/projects/allauth/django-"
"allauth/th/>\n"
"Language: th\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.8-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "บัญชีนี้อยู่ในสถานะที่ใช้งานไม่ได้"

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "คุณไม่สามารถลบที่อยู่อีเมลหลักของคุณได้"

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "อีเมลนี้ได้ถูกเชื่อมกับบัญชีนี้แล้ว"

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "อีเมลและ/หรือรหัสผ่านที่ระบุมาไม่ถูกต้อง"

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "ชื่อผู้ใช้และ/หรือรหัสผ่านที่ระบุมาไม่ถูกต้อง"

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "ชื่อผู้ใช้ได้ถูกลงทะเบียนด้วยอีเมลนี้แล้ว"

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "โปรดใส่รหัสผ่านปัจจุบัน"

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "รหัสไม่ถูกต้อง"

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "รหัสปัจจุบันรหัสผ่านไม่ถูกต้อง"

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "รหัสไม่ถูกต้องหรือหมดอายุ"

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "โทเค็นไม่ถูกต้อง TInvalid"

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "token ที่ใช้รีเซ็ทรหัสผ่านไม่ถูกต้อง"

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "บัญชีของคุณไม่มีการยืนยัน ไม่สามารถเพิ่มที่อยู่อีเมลมากกว่า %d รายการ"

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "ชื่อผู้ใช้ได้ถูกลงทะเบียนด้วยอีเมลนี้แล้ว"

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "พยายามเข้าสู่ระบบล้มเหลวหลายครั้งเกินไป ลองอีกครั้งในภายหลัง"

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "อีเมลนี้ไม่ได้เชื่อมกับบัญชีใดเลย"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "อีเมลนี้ไม่ได้เชื่อมกับบัญชีใดเลย"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "อีเมลหลักของคุณต้องได้การยืนยัน"

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "ไม่สามารถใช้ชื่อผู้ใช้นี้ได้ กรุณาใช้ชื่อผู้ใช้อื่น"

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "ชื่อผู้ใช้และ/หรือรหัสผ่านที่ระบุมาไม่ถูกต้อง"

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "ใช้รหัสผ่านของคุณ"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "ใช้แอปหรือรหัสยืนยันตัวตน"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "ใช้คีย์ความปลอดภัย"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "อีเมลหลักของคุณต้องได้การยืนยัน"

#: account/apps.py:11
msgid "Accounts"
msgstr "บัญชี"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "ต้องพิมพ์รหัสผ่านเดิมซ้ำอีกครั้ง"

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "รหัสผ่าน"

#: account/forms.py:100
msgid "Remember Me"
msgstr "จดจำการเข้าใช้"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "อีเมล"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "อีเมล"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "ชื่อผู้ใช้"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "ลงชื่อเข้าใช้"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "ชื่อผู้ใช้ หรือ อีเมล"

#: account/forms.py:156
msgid "Username or email"
msgstr "ชื่อผู้ใช้ หรือ อีเมล"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "ชื่อผู้ใช้ หรือ อีเมล"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "อีเมล (ไม่จำเป็น)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "ลืมรหัสผ่านของคุณ?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "อีเมล์ (ไม่บังคับ)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "การยืนยันที่อยู่อีเมล"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "อีเมล (ไม่จำเป็น)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "อีเมล (ไม่จำเป็น)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "คุณจะต้องพิมพ์รหัสผ่านอีเมลเดียวกันทุกครั้ง"

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "รหัสผ่าน (อีกครั้ง)"

#: account/forms.py:645
msgid "Current Password"
msgstr "รหัสผ่านปัจจุบัน"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "รหัสผ่านใหม่"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "รหัสผ่านใหม่ (อีกครั้ง)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "รหัส"

#: account/models.py:26
msgid "user"
msgstr "ผู้ใช้"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "อีเมล"

#: account/models.py:34
msgid "verified"
msgstr "ยืนยันแล้ว"

#: account/models.py:35
msgid "primary"
msgstr "หลัก"

#: account/models.py:41
msgid "email addresses"
msgstr "อีเมล"

#: account/models.py:151
msgid "created"
msgstr "สร้างแล้ว"

#: account/models.py:152
msgid "sent"
msgstr "ส่งแล้ว"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "คีย์"

#: account/models.py:158
msgid "email confirmation"
msgstr "การยืนยันอีเมล"

#: account/models.py:159
msgid "email confirmations"
msgstr "การยืนยันอีเมล"

#: headless/apps.py:7
msgid "Headless"
msgstr "หัวขาด"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr "คุณไม่สามารถเพิ่มที่อยู่อีเมลลงในบัญชีที่ได้รับการป้องกันโดยการรับรองความถูกต้องด้วยสองปัจจัย"

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "คุณไม่สามารถปิดใช้งานการรับรองความถูกต้องด้วยสองปัจจัยได้"

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr "คุณไม่สามารถสร้างรหัสกู้คืนได้โดยไม่ต้องเปิดใช้งานการรับรองความถูกต้องด้วยสองปัจจัย"

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr "คุณไม่สามารถเปิดใช้งานการรับรองความถูกต้องด้วยสองปัจจัยได้จนกว่าคุณจะยืนยันที่อยู่อีเมลของคุณแล้ว"

#: mfa/adapter.py:141
msgid "Master key"
msgstr "มาสเตอร์คีย์"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "คีย์สำรอง"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "หมายเลขคีย์ {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "รหัสการกู้คืน"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "เครื่องยืนยันตัวตน TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "รหัสตรวจสอบความถูกต้อง"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "ไร้รหัสผ่าน"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"การเปิดใช้งานการทำงานแบบไร้รหัสผ่านทำให้คุณสามารถลงชื่อเข้าใช้โดยใช้เพียงคีย์นี้ "
"แต่ต้องมีข้อกำหนดเพิ่มเติม เช่น ไบโอเมตริกซ์หรือการป้องกัน PIN"

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr "มีบัญชีที่ใช้อีเมลนี้แล้ว โปรดลงชื่อเข้าใช้บัญชีนั้นก่อน จากนั้นเชื่อมต่อกับบัญชี %s ของคุณ"

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "โทเค็นไม่ถูกต้อง TInvalid"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "บัญชีของคุณไม่ได้ตั้งรหัสผ่านไว้"

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "บัญชีของคุณไม่มีอีเมลที่ยืนยันแล้ว"

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"คุณสามารถลงชื่อเข้าใช้บัญชีของคุณได้โดยใช้วิธีต่อไปนี้: "
"ไม่ตัดการเชื่อมต่อบัญชีบุคคลที่สามที่เหลืออยู่ล่าสุดของคุณ:"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "บัญชีบุคคลที่สามของ Social เชื่อมโยงกับบัญชีอื่นอยู่แล้ว"

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "บัญชีโซเชียล"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "ผู้ให้บริการ"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ผู้ให้บริการ ไอดี"

#: socialaccount/models.py:56
msgid "name"
msgstr "ชื่อ"

#: socialaccount/models.py:58
msgid "client id"
msgstr "รหัสลูกค้า"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "รหัสแอปหรือรหัสผู้บริโภค"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "รหัสลับ"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "ความลับของ API, ความลับของไคลเอ็นต์ หรือความลับของผู้บริโภค"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "คีย์"

#: socialaccount/models.py:81
msgid "social application"
msgstr "การประยุกต์ใช้ทางสังคม"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "การประยุกต์ใช้ทางสังคม"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "เข้าสู่ระบบครั้งล่าสุด"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "วันที่เข้าร่วม"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "ข้อมูลเพิ่มเติม"

#: socialaccount/models.py:125
msgid "social account"
msgstr "บัญชีโซเชียล"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "บัญชีโซเชียล"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) หรือโทเค็นการเข้าถึง (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "ความลับของโทเค็น"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) หรือรีเฟรชโทเค็น (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "หมดอายุเมื่อ"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "โทเค็นแอปพลิเคชันโซเชียล"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "โทเค็นแอปพลิเคชันโซเชียล"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "ข้อมูลโปรไฟล์ไม่ถูกต้อง"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "ลงชื่อเข้าใช้"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "ยกเลิก"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "การตอบสนองไม่ถูกต้องขณะได้รับคำขอโทเค็นจาก \"%s\" การตอบสนองคือ: %s"

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "การตอบสนองผิดพลาดขณะที่กำลังได้รับ access token จาก \"%s\""

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "ไม่มีการบันทึก request token ของ \"%s\""

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "ไม่มีการบันทึก access token ของ \"%s\""

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "ไม่มีสิทธิ์การเข้าใช้ทรัพยากรส่วนตัว ที่ \"%s\""

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "การตอบสนองผิดพลาดขณะที่กำลังได้รับ request token จาก \"%s\""

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "บัญชีไม่มีการใช้งาน"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "บัญชีนี้ไม่มีการใช้งาน"

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr "เราส่งรหัสไปที่ %(email_link)s รหัสจะหมดอายุในไม่ช้า โปรดกรอกรหัสโดยเร็ว"

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "ยืนยัน"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "ขอรหัส"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "ยืนยันที่อยู่อีเมล AddrAccess"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "โปรดตรวจสอบสิทธิ์อีกครั้งเพื่อปกป้องบัญชีของคุณ"

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "ทางเลือกอื่น"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "ยืนยันอีเมล์"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "กรอกรหัสยืนยันอีเมล"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "อีเมล"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "ลงชื่อเข้าใช้"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "ป้อนรหัสเข้าสู่ระบบ"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "รีเซ็ทรหัสผ่าน"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "รีเซ็ทรหัสผ่าน"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "ส่งการยืนยันอีกครั้ง"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "กรอกรหัสยืนยันอีเมล"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "อีเมล"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "อีเมลต่อไปนี้ ได้เชื่อมกับบัญชีของคุณ"

#: templates/account/email.html:25
msgid "Verified"
msgstr "ยืนยันแล้ว"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "ยังไม่ได้ยืนยัน"

#: templates/account/email.html:34
msgid "Primary"
msgstr "หลัก"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "ทำให้เป็นอันหลัก"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "ส่งการยืนยันอีกครั้ง"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "ลบ"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "เพิ่มอีเมล"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "เพิ่มอีเมล"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "คุณต้องการที่จะลบอีเมลนี้จริงหรอ"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"คุณได้รับอีเมลนี้ เพราะคุณหรือมีคนอื่นพยายามลงทะเบียน\n"
"บัญชีโดยใช้ที่อยู่อีเมล:\n"
"\n"
"%(email)s\n"
"\n"
"อย่างไรก็ตาม มีบัญชีที่ใช้อีเมลดังกล่าวอยู่แล้ว  ในกรณีที่คุณ\n"
"ลืมอีเมลนี้ไป โปรดใช้ขั้นตอนลืมรหัสผ่านเพื่อกู้คืน\n"
"บัญชีของคุณ:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "มีบัญชีอยู่แล้ว"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "สวัสดีจาก %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"ขอบคุณที่ใช้บริการของ %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "คุณได้รับอีเมลนี้ เนื่องจากมีการเปลี่ยนแปลงต่อไปนี้กับบัญชีของคุณ"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"หากคุณไม่รู้จักการเปลี่ยนแปลงนี้ โปรดดำเนินการตามมาตรการรักษาความปลอดภัยที่เหมาะสมทันที "
"การเปลี่ยนแปลงในบัญชีของคุณมีต้นตอมาจาก:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "อีเมล์ของคุณถูกเปลี่ยนจาก %(from_email)s to %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "ที่อยู่อีเมลเปลี่ยนแปลง"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "คุณได้ทำการยืนยัน %(email)s"

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "การยืนยันทางอีเมล์"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"คุณได้รับอีเมลนี้เพราะผู้ใช้ %(user_display)s ให้ที่อยู่อีเมลของคุณเพื่อลงทะเบียนบัญชีใน "
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr "รหัสยืนยันอีเมลของคุณแสดงอยู่ด้านล่างนี้ โปรดป้อนรหัสดังกล่าวในหน้าต่างเบราว์เซอร์ที่เปิดอยู่"

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "เพื่อยืนยันว่าถูกต้อง โปรดไปที่ %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "กรุณายืนยันอีเมลของคุณ"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "ที่อยู่อีเมล %(deleted_email)s ถูกลบออกจากบัญชีของคุณ"

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "ลบ อีเมล์"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr "รหัสลงชื่อเข้าใช้ของคุณแสดงอยู่ด้านล่างนี้ โปรดป้อนรหัสดังกล่าวในหน้าต่างเบราว์เซอร์ที่เปิดอยู่"

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "คุณสามารถละเว้นเมลนี้ได้อย่างปลอดภัยหากคุณไม่ได้เป็นผู้เริ่มดำเนินการนี้"

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "ลงชื่อเข้าใช้"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "รหัสผ่านของคุณได้เปลี่ยนแล้ว"

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "รหัสผ่าน (อีกครั้ง)"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr "รหัสลงชื่อเข้าใช้ของคุณแสดงอยู่ด้านล่างนี้ โปรดป้อนรหัสดังกล่าวในหน้าต่างเบราว์เซอร์ที่เปิดอยู่"

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "รีเซ็ทรหัสผ่าน"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"สวัสดีจาก %(site_name)s!\n"
"\n"
"You're receiving this e-mail because you or someone else has requested a "
"คุณได้รับอีเมลนี้เพราะว่า คุณหรือใครบางคนได้ทำการร้องขอรหัสผ่านของบัญชีนี้ที่ %(site_domain)s.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"คุณสามารถมองข้ามและลบอีเมลนี้ทิ้งได้เลยหากคุณไม่ได้ทำการร้องขอการรีเซ็ทรหัสผ่านคลิกที่ลิงค์ข้างล่างนี้เพื่อรีเซ็ทรหัสผ่านของคุณ"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "ในกรณีเผื่อคุณลืม ชื่อผู้ใช้ของคุณคือ %(username)s"

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "อีเมลในการรีเซ็ทรหัสผ่าน"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "รหัสผ่านของคุณได้ถูกเปลี่ยนและรีเซ็ตแล้ว"

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "รหัสผ่านของคุณได้เปลี่ยนแล้ว"

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "รีเซ็ทรหัสผ่าน"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"คุณได้รับอีเมลนี้เนื่องจากคุณหรือบุคคลอื่นพยายามเข้าถึงบัญชีที่มีอีเมล %(email)s อย่างไรก็ตาม "
"เราไม่มีบันทึกเกี่ยวกับบัญชีดังกล่าวในฐานข้อมูลของเรา"

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "หากเป็นคุณ คุณสามารถลงทะเบียนบัญชีโดยใช้ลิงก์ด้านล่าง"

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "ไม่ทราบ บัญชี"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "ที่อยู่อีเมล"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "รหัสผ่านอีเมลปัจจุบัน"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "เปลี่ยนเป็น"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "ที่อยู่อีเมลหลักของคุณยังต้องอยู่ระหว่างรอการยืนยัน"

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "ยกเลิกการเปลี่ยนแปลง"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "อีเมล์เปลี่ยนเป็น"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "เปลี่ยนอีเมล์"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "ยืนยันอีเมล"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"กรุณายืนยันว่า <a href=\"mailto:%(email)s\">%(email)s</a> เป็นที่อยู่อีเมลของผู้ใช้ "
"%(user_display)s"

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "ไม่สามารถยืนยัน %(email)s ได้เพราะได้รับการยืนยันโดยบัญชีอื่นแล้ว"

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"ลิงก์ยืนยันอีเมลนี้หมดอายุหรือไม่ถูกต้อง กรุณา <a "
"href=\"%(email_url)s\">ส่งคำขอยืนยันอีเมลใหม่</a>"

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "ถ้าหากคุณยังไม่มีบัญชี, กรุณา %(link)sลงทะเบียน%(end_link)sก่อน."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "ลงชื่อเข้าใช้ด้วยรหัสผ่าน"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "ส่งรหัสลงชื่อเข้าใช้ให้ฉันทางอีเมล์"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "ลงชื่อออก"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "คุณแน่ใจหรือว่าต้องการจะลงชื่อออก"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "คุณไม่สามารถลบอีเมลของคุณได้ (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "อีเมลยืนยันได้ถูกส่งไปที่ %(email)s"

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "คุณได้ทำการยืนยัน %(email)s"

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "ลบอีเมล %(email)s แล้ว"

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "%(name)s ลงชื่อเข้าใช้สำเร็จ"

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "คุณได้ออกจากระบบแล้ว"

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "รหัสลงชื่อเข้าใช้ได้ถูกส่งไปยัง %(email)s แล้ว"

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "เปลี่ยนรหัสผ่านสำเร็จ"

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "ตั้งรหัสผ่านสำเร็จ"

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "รหัสลงชื่อเข้าใช้ได้ถูกส่งไปยัง %(email)s แล้ว"

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "ตั้งอีเมลหลักสำเร็จ"

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "เปลี่ยนรหัสผ่าน"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "ลืมรหัสผ่าน?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr "ลืมรหัสผ่านหรือไม่? ป้อนที่อยู่อีเมลของคุณด้านล่าง และเราจะส่งอีเมลไปให้คุณเพื่อรีเซ็ตรหัสผ่าน"

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "รีเซ็ทรหัสผ่านของฉัน"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "กรุณาติดต่อเราหากคุณพบปัญหาในการรีเซ็ทรหัสผ่าน"

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"เราได้ส่งอีเมลถึงคุณแล้ว หากต้องการยืนยันตัวตน โปรดคลิกลิงก์ในอีเมลนี้ โปรดไปที่โฟลเดอร์สแปม "
"มิฉะนั้น โปรดติดต่อเราหากไม่ได้รับอีเมลภายในไม่กี่นาที"

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Token เสีย"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"ลิงค์ที่ใช้ในการรีเซ็ทรหัสผ่านไม่ถูกต้อง อาจเป็นไปได้ว่ามันได้ถูกใช้ไปแล้วกรุณาร้องขอ <a "
"href=\"%(passwd_reset_url)s\">การรีเซ็ทรหัสผ่านใหม่</a>"

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "รหัสผ่านของคุณได้เปลี่ยนแล้ว"

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "ตั้งรหัสผ่าน"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "อีเมล์เปลี่ยนเป็น"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "รหัสผ่านปัจจุบัน"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "ที่อยู่อีเมลหลักของคุณยังต้องอยู่ระหว่างรอการยืนยัน"

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "ลืมรหัสผ่าน?:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr "คุณจะได้รับอีเมลที่มีรหัสพิเศษสำหรับการลงชื่อเข้าใช้แบบไม่ต้องใช้รหัสผ่าน"

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "ขอรหัส"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "ตัวเลือกการลงชื่อเข้าใช้อื่นๆ"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "ลงทะเบียน"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "ลงทะเบียน"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "มีบัญชีอยู่แล้ว? กรุณา%(link)sลงชื่อเข้าใช้%(end_link)s"

#: templates/account/signup.html:39
#, fuzzy
#| msgid "Sign in with a passkey"
msgid "Sign up using a passkey"
msgstr "ลงชื่อเข้าใช้ด้วยรหัสผ่าน"

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "ลงทะเบียน"

#: templates/account/signup_by_passkey.html:36
#, fuzzy
#| msgid "Other sign-in options"
msgid "Other options"
msgstr "ตัวเลือกการลงชื่อเข้าใช้อื่นๆ"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "การลงทะเบียนปิดอยู่"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "เราขอโทษด้วย การลงทะเบียนได้ปิดชั่วคราว"

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "หมายเหตุ"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "คุณเข้าสู่ระบบแล้วในชื่อ %(user_display)s"

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "คำเตือน:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr "ขณะนี้คุณยังไม่ได้ตั้งค่าที่อยู่อีเมล คุณควรเพิ่มที่อยู่อีเมลเพื่อรับการแจ้งเตือน รีเซ็ตรหัสผ่าน ฯลฯ"

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "ยืนยันอีเมลของคุณ"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"เราได้ส่งอีเมลถึงคุณเพื่อยืนยันแล้ว ทำตามลิงก์ที่ให้ไว้เพื่อดำเนินการลงทะเบียนให้เสร็จสิ้น "
"หากคุณไม่พบอีเมลยืนยันในกล่องจดหมายหลัก โปรดตรวจสอบในโฟลเดอร์สแปม "
"โปรดติดต่อเราหากคุณไม่ได้รับอีเมลยืนยันภายในไม่กี่นาที"

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"ส่วนนี้ของไซต์ต้องการให้เราตรวจสอบว่าคุณคือบุคคลที่คุณอ้างว่าเป็น เพื่อจุดประสงค์นี้ "
"เราต้องการให้คุณ\n"
"ยืนยันความเป็นเจ้าของที่อยู่อีเมลของคุณ "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"เราได้ส่งอีเมลถึงคุณเพื่อยืนยันแล้ว โปรดคลิกลิงก์ในอีเมลนี้ โปรดส่งอีเมล "
"หากคุณไม่พบอีเมลยืนยันในกล่องจดหมายหลัก โปรดตรวจสอบในโฟลเดอร์สแปม มิฉะนั้น "
"โปรดติดต่อเราหากไม่ได้รับอีเมลภายในไม่กี่นาที"

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>หมายเหตุ:</strong> คุณยังสามารถ<a href=\"%(email_url)s\">เปลี่ยนที่อยู่อีเมล</"
"a>ของคุณได้"

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "ข้อความ:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "เมนู:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "การเชื่อมต่อบัญชีต่างๆ"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "การตรวจสอบสิทธิ์สองปัจจัย"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "เซสชั่น"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr "บัญชีของคุณได้รับการปกป้องด้วยการตรวจสอบสิทธิ์สองขั้นตอน โปรดป้อนรหัสตรวจสอบสิทธิ์:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "มีการสร้างรหัสการกู้คืนการตรวจสอบสิทธิ์แบบสองปัจจัยชุดใหม่แล้ว"

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "รหัสการกู้คืนใหม่ถูกสร้างขึ้น"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "เปิดใช้งานแอปพลิเคชัน Authenticator แล้ว"

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "เปิดใช้งานแอป Authenticator แล้ว"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "ปิดใช้งานแอปพลิเคชัน Authenticator แล้ว"

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "ปิดใช้งานแอป Authenticator แล้ว"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "มีการเพิ่มคีย์ความปลอดภัยใหม่แล้ว"

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "เพิ่มรหัสความปลอดภัยแล้ว"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "คุณได้ยืนยันแล้วว่า %(email)sรหัสความปลอดภัยถูกลบออกแล้ว"

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "ถอดคีย์ความปลอดภัยออกแล้ว"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Authenticator แอป"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "การยืนยันตัวตนโดยใช้แอปพลิเคชันการยืนยันตัวตนเปิดใช้งานอยู่"

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "แอปพลิเคชันการตรวจสอบสิทธิ์ไม่ได้เปิดใช้งาน"

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "ปิดการใช้งาน"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "เปิดใช้งาน"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "กุญแจความปลอดภัย"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "คุณได้เพิ่มรหัสความปลอดภัย %(count)s แล้ว"

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "ไม่มีการเพิ่มคีย์ความปลอดภัย"

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "จัดการ"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "แอป"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "รหัสการกู้คืน"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "มีรหัสกู้คืน %(unused_count)s จากทั้งหมด %(total_count)s รหัส"

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "ไม่มีการตั้งค่ารหัสการกู้คืน"

#: templates/mfa/index.html:96
msgid "View"
msgstr "ดู"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "ดาวน์โหลด"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "สร้าง"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "มีการสร้างชุดรหัสการกู้คืนใหม่แล้ว"

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "เพิ่มคีย์ความปลอดภัยแล้ว"

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "ถอดคีย์ความปลอดภัยออกแล้ว"

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "กรอกรหัสยืนยันตัวตน:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "คุณกำลังจะสร้างชุดรหัสการกู้คืนใหม่สำหรับบัญชีของคุณ"

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "การดำเนินการนี้จะทำให้รหัสที่มีอยู่ของคุณไม่ถูกต้อง"

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "คุณแน่ใจมั้ย?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "รหัสที่ไม่ได้ใช้"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "รหัสดาวน์โหลด"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "สร้างรหัสใหม่"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "เปิดใช้งานแอป Authenticator"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"หากต้องการปกป้องบัญชีของคุณด้วยการตรวจสอบสิทธิ์สองขั้นตอน ให้สแกนรหัส QR "
"ด้านล่างด้วยแอปตรวจสอบสิทธิ์ของคุณ จากนั้นป้อนรหัสยืนยันที่สร้างโดยแอปด้านล่าง"

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "ความลับของผู้รับรอง"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr "คุณสามารถเก็บความลับนี้ไว้และนำไปใช้ในการติดตั้งแอปพลิเคชันการตรวจสอบความถูกต้องอีกครั้งในภายหลังได้"

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "ปิดใช้งานแอป Authenticator"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "คุณกำลังจะปิดการใช้งานการตรวจสอบสิทธิ์โดยใช้แอปตัวตรวจสอบสิทธิ์ คุณแน่ใจหรือไม่?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "เพิ่มรหัสความปลอดภัย"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "ถอดกุญแจความปลอดภัยออก"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "คุณแน่ใจว่าต้องการลงชื่อออกหรือลบคีย์ความปลอดภัยนี้หรือไม่"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "การใช้งาน"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "กุญแจผี"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "กุญแจความปลอดภัย"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "คีย์นี้ไม่ได้ระบุว่าเป็นรหัสผ่านหรือไม่"

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "ไม่ระบุ"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "เพิ่มเมื่อ %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "ใช้ล่าสุด %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "แก้ไข"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "แก้ไขรหัสความปลอดภัย"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "บันทึก"

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Passkey"
msgid "Create Passkey"
msgstr "กุญแจผี"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "สร้างแล้ว"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "ฟังก์ชันนี้ต้องใช้ JavaScript"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "การเข้าสู่ระบบของบุคคลที่สามในเครือข่ายสังคมออนไลน์ล้มเหลว"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "เกิดข้อผิดพลาดขณะพยายามเข้าสู่ระบบผ่านเครือข่ายโซเชียลหรือบัญชีบุคคลที่สามของคุณ"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "คุณสามารถลงชื่อเข้าใช้บัญชีของคุณโดยใช้บัญชีบุคคลที่สามต่อไปนี้:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "ขณะนี้คุณไม่มีบัญชีบุคคลที่สามหรือเครือข่ายโซเชียลที่เชื่อมโยงกับบัญชีนี้"

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "เพิ่มบัญชีบุคคลที่สาม"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "บัญชีบุคคลที่สามจาก %(provider)s เชื่อมต่อกับบัญชีของคุณแล้ว"

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "เพิ่มบัญชีบุคคลที่สามที่เชื่อมต่อ"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "บัญชีบุคคลที่สามจาก %(provider)s ถูกตัดการเชื่อมต่อจากบัญชีของคุณ"

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "เพิ่มบัญชีบุคคลที่สามที่ถูกตัดการเชื่อมต่อ"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "เชื่อมต่อ %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "คุณกำลังจะเชื่อมต่อบัญชีบุคคลที่สามใหม่จาก %(provider)s"

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "ลงชื่อเข้าใช้ผ่าน %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "คุณกำลังจะลงชื่อเข้าใช้โดยใช้บัญชีบุคคลที่สามจาก %(provider)s"

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "ดำเนินการต่อ"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "การลงชื่เข้าใช้ยกเลิก"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"คุณตัดสินใจที่จะยกเลิกการลงชื่อเข้าใช้เว็บของเราด้วยหนึ่งในบัญชีของคุณถ้านี้เป็นความผิดพลาด กรุณา<a "
"href=\"%(login_url)s\">ลงชื่อเข้าใช้</a>"

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "บัญชีบุคคลที่สามของ Social ได้รับการเชื่อมโยงแล้ว"

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "บัญชีบุคคลที่สามของ Social ถูกตัดการเชื่อมต่อ"

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"คุณกำลังจะทำการใช้บัญชี %(provider_name)s ของคุณ ในการเข้าสู่ระบบของ\n"
"%(site_name)s. ในขั้นตอนสุดท้าย กรุณากรอกฟอร์มข้างล่าง:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "หรือใช้บุคคลที่สาม"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "ออกจากระบบจากเซสชันอื่นทั้งหมด"

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "เริ่มต้นที่"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "อีเมล์ที่อยู่ IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "บราวเซอร์"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "พบเห็นครั้งสุดท้ายเมื่อ"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "รหัสผ่านปัจจุบัน"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "ออกจากระบบเซสชั่นอื่น ๆ"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "เซสชันผู้ใช้"

#: usersessions/models.py:92
msgid "session key"
msgstr "คีย์เซสชั่น"

#~ msgid "Account Connection"
#~ msgstr "การเชื่อมต่อบัญชี"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "รหัสผ่านต้องมีอย่างน้อย {0} ตัวอักษร"

#, fuzzy, python-format
#~| msgid ""
#~| "Hello from %(site_name)s!\n"
#~| "\n"
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account at %(site_domain)s.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "สวัสดีจาก %(site_name)s!\n"
#~ "\n"
#~ "You're receiving this e-mail because you or someone else has requested a "
#~ "คุณได้รับอีเมลนี้เพราะว่า คุณหรือใครบางคนได้ทำการร้องขอรหัสผ่านของบัญชีนี้ที่ "
#~ "%(site_domain)s.\n"
#~ "It can be safely ignored if you did not request a password reset. Click "
#~ "the "
#~ "คุณสามารถมองข้ามและลบอีเมลนี้ทิ้งได้เลยหากคุณไม่ได้ทำการร้องขอการรีเซ็ทรหัสผ่านคลิกที่ลิงค์ข้างล่างนี้เพื่อรีเซ็ทรหัสผ่านของคุณ"

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "อีเมลต่อไปนี้ได้เชื่อมกับบัญชีของคุณ"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "ยืนยันอีเมล"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "กรุณาลงชื่อเข้าใช้ด้วยบัญชีภายนอกของคุณ\n"
#~ "หรือ Or, %(link)sลงทะเบียน %(end_link)s\n"
#~ "สำหรับบัญชีของ %(site_name)s และลงชื่อเข้าใช้ด้านล่าง:"

#~ msgid "or"
#~ msgstr "sinv"

#~ msgid "change password"
#~ msgstr "เปลี่ยนรหัสผ่าน"

#~ msgid "OpenID Sign In"
#~ msgstr "ลงชื่อเข้าใช้ด้วย OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "อีเมลนี้ได้ถูกเชื่อมกับบัญชีอื่นแล้ว"

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr "เราได้ส่งอีเมลให้คุณแล้ว. กรุณาติดต่อเราหากคุณไม่ได้รับอีเมลภายในเวลาไม่กี่นาทีนี้"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "การลงชื่เข้าใช้และ/หรือรหัสผ่านที่ระบุมาไม่ถูกต้อง"

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "ชื่อผู้ใช้สามารถมีตัวอักษรภาษาอังกฤษตัวเลขและ @/./+/-/_. เท่านั้น"

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "ชื่อผู้ใช้นี้ถูกใช้แล้ว กรุณาเลือกชื่ออื่น"

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "ลงชื่อเข้าใช้"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "คุณได้ยืนยันว่า <a href=\"mailto:%(email)s\">%(email)s</a> เป็นอีเมลของผู้ใช้ "
#~ "%(user_display)s."
