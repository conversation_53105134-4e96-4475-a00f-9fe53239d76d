# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-05-23 07:13+0000\n"
"Last-Translator: Us<PERSON> <<EMAIL>>\n"
"Language-Team: Arabic <https://hosted.weblate.org/projects/allauth/"
"django-allauth/ar/>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "هذا الحساب غير نشط حاليا."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "لا يمكنك إزالة عنوان بريدك الإلكتروني الرئيسي."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "عنوان البريد الإلكتروني هذا مربوط بالفعل مع هذا الحساب."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "عنوان البريد الإلكتروني و / أو كلمة المرور غير صحيحة."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "رقم الهاتف و / أو كلمة المرور غير صحيحة."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "هنالك مستخدم مسجل سابقا يستخدم عنوان البريد الإلكتروني نفسه."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "الرجاء كتابة كلمة المرور الحالية."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "كود خاطئ."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "كلمة مرور خاطئة."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "مفتاح خاطئ او منتهي."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "فشل تسجيل الدخول."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "كود إعادة تعيين كلمة المرور غير صالح."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "لا يمكنك إضافة أكثر من %d بريد إلكتروني."

#: account/adapter.py:76
msgid "A user is already registered with this phone number."
msgstr "تم تسجيل مستخدم بالفعل برقم الهاتف هذا."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "تجاوزت الحد المسموح لمحاولة تسجيل الدخول. حاول في وقت لاحق."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "لم يتم ربط عنوان البريد الإلكتروني مع أي حساب مستخدم."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "لم يتم ربط رقم الهاتف مع أي حساب مستخدم."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "يجب توثيق عنوان بريدك الإلكتروني الرئيسي."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "اسم المستخدم غير مسموح به. الرجاء اختيار اسم آخر‪."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "اسم المستخدم و / أو كلمة المرور غير صحيحة."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "الرجاء اختيار واحد فقط."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "يجب أن تكون القيمة الجديدة مختلفة عن القيمة الحالية."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr "كن صبوراً، فأنت ترسل عدداً كبيراً جداً من الطلبات."

#: account/adapter.py:778
msgid "Use your password"
msgstr "استخدم كلمة مرورك"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "استخدم تطبيق المصادقة او الكود"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "استخدم مفتاح الأمان"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "تحديد البريد الإلكتروني المختار بأنه تم التحقق منه"

#: account/apps.py:11
msgid "Accounts"
msgstr "الحسابات"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"الرجاء إدخال رقم هاتف بما في ذلك رقم البلاد (على سبيل المثال ‪+1‬ للولايات "
"المتحدة)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "هاتف"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "يجب عليك كتابة كلمة المرور نفسها في كل مرة‪."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "كلمة المرور"

#: account/forms.py:100
msgid "Remember Me"
msgstr "تذكرني"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "عنوان البريد الالكتروني"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "البريد الالكتروني"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "اسم المستخدم"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "تسجيل الدخول"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "اسم المستخدم أو البريد الإلكتروني أو الهاتف"

#: account/forms.py:156
msgid "Username or email"
msgstr "اسم المستخدم أو البريد الإلكتروني"

#: account/forms.py:158
msgid "Username or phone"
msgstr "اسم المستخدم أو الهاتف"

#: account/forms.py:160
msgid "Email or phone"
msgstr "البريد الالكتروني أو الهاتف"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "هل نسيت كلمة المرور؟"

#: account/forms.py:334
msgid "Email (again)"
msgstr "البريد الإلكتروني ‪(مجددا)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "تأكيد البريد الإلكتروني"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "البريد الالكتروني (اختياري)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "اسم المستخدم (اختياري)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "يجب عليك كتابة البريد الإلكتروني نفسه في كل مرة‪."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "كلمة المرور (مجددا)"

#: account/forms.py:645
msgid "Current Password"
msgstr "كلمة المرور الحالية"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "كلمة المرور الجديدة"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "كلمة المرور الجديدة (مجددا)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "شفرة"

#: account/models.py:26
msgid "user"
msgstr "مستخدم"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "عنوان بريد إلكتروني"

#: account/models.py:34
msgid "verified"
msgstr "موثق"

#: account/models.py:35
msgid "primary"
msgstr "رئيسي"

#: account/models.py:41
msgid "email addresses"
msgstr "عناوين البريد الالكتروني"

#: account/models.py:151
msgid "created"
msgstr "تمّ إنشاؤه"

#: account/models.py:152
msgid "sent"
msgstr "تم ارساله"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "مفتاح"

#: account/models.py:158
msgid "email confirmation"
msgstr "تأكيد البريد الإلكتروني"

#: account/models.py:159
msgid "email confirmations"
msgstr "تأكيدات البريد الإلكتروني"

#: headless/apps.py:7
msgid "Headless"
msgstr "مقطوعة الرأس"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"لا يمكنك إضافة عنوان بريد إلكتروني إلى حساب محمي بواسطة المصادقة الثنائية."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "لا يمكنك إلغاء تنشيط المصادقة الثنائية."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr "لا يمكنك إنشاء رموز الاسترداد دون تمكين المصادقة الثنائية."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"لا يمكنك تفعيل المصادقة الثنائية إلا بعد التحقق من عنوان بريدك الإلكتروني."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "مفتاح رئيسي"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "المفتاح الاحتياطي"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "مفتاح رقم {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "وزارة الخارجية"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "رموز الاسترداد"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "أداة مصادقة TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "مصادقة الويب"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "كود المصدق"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "بدون كلمة مرور"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"يسمح لك تفعيل الدخول من غير كلمة مرور باستعمال هذا المفتاح فقط، ولكنه يفرض "
"شروط دخول أخرى مثل الدخول بالمعرّفات الحيوية (مثل البصمة) أو رقم التعريف "
"الشخصي."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"يوجد حساب بالفعل مربوط مع هذا البريد الإلكتروني. يرجى الدخول إلى ذاك الحساب "
"أولا، ثم ربط حسابك في %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "كود غير صالح."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "حسابك ليست له كلمة مرور مضبوطة."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "حسابك ليس لديه عنوان بريد إلكتروني موثقف."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "لا يمكنك حذف هذا الحساب لطرف ثالث وفصله عن حسابك."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "حساب الطرف الثالث متصل مسبقا مع حساب مختلف."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "حسابات التواصل الاجتماعي"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "مزود"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "معرف المزود"

#: socialaccount/models.py:56
msgid "name"
msgstr "اسم"

#: socialaccount/models.py:58
msgid "client id"
msgstr "معرف العميل"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "معرف آبل، أو مفتاح المستهلك"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "مفتاح سري"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "مفتاح واجهة برمجية سري أو مفتاح مستهلك"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "مفتاح"

#: socialaccount/models.py:81
msgid "social application"
msgstr "تطبيق اجتماعي"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "تطبيقات اجتماعية"

#: socialaccount/models.py:117
msgid "uid"
msgstr "معرف المستخدم"

#: socialaccount/models.py:119
msgid "last login"
msgstr "أخر دخول"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "تاريخ الانضمام"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "بيانات إضافية"

#: socialaccount/models.py:125
msgid "social account"
msgstr "حساب تواصل اجتماعي"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "حسابات تواصل اجتماعي"

#: socialaccount/models.py:160
msgid "token"
msgstr "كود"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) أو مفتاح وصول (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "كود سري"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) أو رمز تحديث (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "ينتهي في"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "كود تطبيق اجتماعي"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "أكواد التطبيقات الاجتماعية"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "معلومات ملف شخصي غير صالحة"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "تسجيل الدخول"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "يلغي"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "تم تلقي رد غير صالح عند الحصول على كود الطلب من \"%s\". الرد كان: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "تم تلقي رد غير صالح عند الحصول على كود الوصول من \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "لا يوجد كود طلب محفوظ لـ \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "لا يوجد كود وصول محفوظ لـ \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "لا وصول للموارد الخاصة في \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "تم تلقي رد غير صالح عند الحصول على كود الطلب من \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "الحساب غير نشط"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "هذا الحساب غير نشط."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"لقد أرسلنا رمزًا إلى %(recipient)s. الرمز ينتهي قريبًا، لذا يرجى إدخاله قريبًا."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "تأكيد"

#: templates/account/base_confirm_code.html:43
msgid "Request new code"
msgstr "طلب كود جديد"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "تأكيد الوصول"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "يرجى إعادة المصادقة لحماية حسابك."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "خيارات بديلة"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "تأكيد البريد الإلكتروني"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "الرجاء إدخال الكود المبعوث إلى بريدك الالكتروني"

#: templates/account/confirm_email_verification_code.html:16
msgid "Use a different email address"
msgstr "استخدم بريد إلكتروني آخر"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "تسجيل الدخول"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "أدخل رمز تسجيل الدخول"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "إعادة تعيين كلمة المرور"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "الرجاء إدخال الكود لإعادة تعيين كلمة المرور"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "التأكيد من رقم الهاتف"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "الرجاء إدخال الكود للتأكيد من رقم الهاتف"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr "استخدم رقم هاتف آخر"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "عناوين البريد الإلكتروني"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "عناوين البريد الإلكتروني التالية مربوطة مع حسابك:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "موثق"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "غير موثق"

#: templates/account/email.html:34
msgid "Primary"
msgstr "أساسي"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "اجعله أساسيا"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "إعادة ارسال رسالة التأكيد"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "احذف"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "أضف عنوان بريد إلكتروني"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "أضف بريدا إلكترونيا"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "هل تريد حقا حذف عنوان البريد الإلكتروني المحدد؟"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"تلقيت هذه الرسالة لأنك أو أحد آخر حاول تسجيل حساب\n"
"باستخدام البريد الإلكتروني:\n"
"\n"
"%(email)s\n"
"\n"
"لكن حسابا مربوطا بهذا البريد موجود بالفعل. \n"
"في حال نسيت هذا، يمكنك استخدام رابط\n"
"إعادة ضبط كلمة المرور لاستعادة حسابك:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "الحساب موجود بالفعل"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "مرحبا من موقع %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"شكرا لاستخدامك %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "أنت تتلقى هذا البريد لأنه تم إجراء التغيير التالي على حسابك:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"إذا لم تتعرف على هذا التغيير، فيرجى اتخاذ الاحتياطات الأمنية المناسبة على "
"الفور. التغيير في حسابك ناتج عن:\n"
"\n"
"- عنوان IP: %(ip)s\n"
"- المتصفح: %(user_agent)s\n"
"- التاريخ: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "J لقد تم تغيير بريدك الإلكتروني من %(from_email)s إلى %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "تم تغيير عنوان البريد الالكتروني"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "تم التأكيد من عنوان بريدك الالكتروني."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "تأكيد البريد الإلكتروني"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"تلقيت هذه الرسالة لأن المستخدم %(user_display)s أعطانا بريدك الإلكتروني "
"لتسجيل حساب في %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"الكود للتأكيد من بريدك الالكتروني مدرج أدناه. الرجاء إدخاله في نافذة المتصفح "
"المفتوحة."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "للتأكيد بأن هذا صحيح، اذهب إلى %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "أكد عنوان البريد الإلكتروني"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "تمت إزالة عنوان البريد الإلكتروني %(deleted_email)s من حسابك."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "تم حذف البريد الالكتروني"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"رمز تسجيل الدخول الخاص بك مدرج أدناه. الرجاء إدخاله في نافذة المتصفح "
"المفتوحة."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "يمكن تجاهل هذا البريد بأمان إذا لم تبدأ هذا الإجراء."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "كود تسجيل الدخول"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "تم تغيير كلمة المرور الخاصة بك."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "تم تغيير كلمة المرور"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"الكود لإعادة تعيين كلمة المرور مدرج أدناه. الرجاء إدخاله في نافذة المتصفح "
"المفتوحة."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "كود إعادة تعيين كلمة المرور"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"تلقيت هذه الرسالة لأنك أو أحد آخر طلب إعادة ضبط كلمة مرور حسابك.\n"
"يمكنك تجاهل الرسالة بأمان إذا لم تطلب هذا. اضغط الرابط في الأسفل لإعادة ضبط "
"كلمة المرور."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "في حال كنت قد نسيت، اسم المستخدم الخاص بك هو %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "رسالة إعادة ضبط كلمة المرور"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "تم إعادة تعيين كلمة المرور الخاصة بك."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "تم تحديد كلمة المرور الخاصة بك."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "تم تحديد كلمة المرور"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"O أنت تتلقى هذا البريد الإلكتروني لأنك، أو حاول شخص آخر، الوصول إلى حساب "
"باستخدام البريد الإلكتروني %(email)s. ومع ذلك، ليس لدينا أي سجل لمثل هذا "
"الحساب في قاعدة بياناتنا."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "إذا كنت أنت، فيمكنك التسجيل للحصول على حساب باستخدام الرابط أدناه."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "حساب غير معروف"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "بريد إلكتروني"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "البريد الالكتروني الحالي"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "التغيير إلى"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "ما زال عنوان البريد الإلكتروني الخاص بك بانتظار التوثيق."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "إلغاء التغيير"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "التغيير إلى"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "تغيير البريد الإلكتروني"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "تأكيد البريد الإلكتروني"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"يرجى تأكيد أن <a href=\"mailto:%(email)s\">%(email)s</a> هو عنوان بريد "
"إلكتروني للمستخدم %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "فشل تأكيد %(email)s لأنه مؤكد بالفعل في حساب مختلف."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"هذا الرابط لتأكيد البريد الإلكتروني نتهت فترة صلاحيته أو إنه غير صالح. يرجى "
"<a href=\"%(email_url)s\">طلب رابط جديد لتأكيد البريد الإلكتروني</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "إذا لم يكن لديك حساب، يرجى %(link)sالتسجيل%(end_link)s أولا."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "تسجيل الدخول باستعمال مفتاح مرور"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "أرسل لي كود تسجيل الدخول"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "تسجيل الخروج"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "هل تريد حقا تسجيل الخروج؟"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "لا يمكنك إزالة عنوان البريد الإلكتروني الرئيسي (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "تم إرسال رسالة تأكيد إلى العنوان %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "لقد أكدت عنوان البريد الإلكتروني %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "تمت إزالة عنوان البريد الإلكتروني %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "لقد سجلت الدخول بنجاح يا %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "تم تسجيل خروجك."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "تم إرسال كود تسجيل الدخول إلى %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "تم تغيير كلمة المرور بنجاح‪."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "تم إعداد كلمة المرور بنجاح‪."

#: templates/account/messages/phone_verification_sent.txt:2
#, python-format
msgid "A verification code has been sent to %(phone)s."
msgstr "تم إرسال رمز التحقق إلى %(phone)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "لقد تحققت من رقم الهاتف %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "تم تعيين عنوان البريد الإلكتروني الرئيسي‪."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "غيّر كلمة المرور"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "هل نسيت كلمة المرور؟"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"هل نسيت كلمة المرور؟ أدخل عنوان البريد الإلكتروني أدناه، وسنرسل لك رسالة "
"تتيح لك إعادة تعيينها."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "إعادة تعيين كلمة المرور"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "يرجى الاتصال بنا إذا كنت تواجه أي مشاكل في إعادة تعيين كلمة المرور."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"أرسلنا لك رسالة. إذا لم تصلك يرجى التحقق من صندوق الرسائل غير المرغوب فيها. "
"اتصل بنا إذا لم تصل في غضون دقائق."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "كود غير صالح"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"رابط إعادة تعيين كلمة المرور غير صالح، ربما لأنه قد تم استخدامه مسبقا. يرجى "
"طلب <a href=\"%(passwd_reset_url)s\">رابط جديد</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "تم تغيير كلمة مرورك الآن."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "تعيين كلمة مرور"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "تغيير الهاتف"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "رقم الهاتف الحالي"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "ما زال رقم الهاتف الخاص بك بانتظار التوثيق."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "أدخل كلمة المرور الخاص بك:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "ستتلقى كود خاص لتسجيل الدخول بدون كلمة مرور."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "كود الطلب"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "خيارات تسجيل الدخول الأخرى"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "التسجيل"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "الاشتراك"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "لديك حساب؟ %(link)sسجل الدخول%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "تسجيل باستعمال مفتاح مرور"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "التسجيل عن خلال مفتاح مرور"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "خيارات أخرى"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "الاشتراك مغلق"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "نحن آسفون‪:‬ الاشتراك مغلق حاليا‪."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "ملاحظة"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "تم تسجيل دخولك بالفعل يا %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "تحذير‪:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"ليس لديك أي بريد إلكتروني مضبوط. عليك حقا إضافة عنوان لكي تتلقى الإشعارات، "
"وتعيد تعيين كلمة المرور، إلخ."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "التحقق من البريد الإلكتروني"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"لقد أرسلنا لك رسالة للتوثيق. اتبع الرابط المزود لإكمال عملية التسجيل. إذا لم "
"ترى الرسالة في صندوقك الرئيسي، تحقق من صندوق الرسائل غير المرغوب فيها. يرجى "
"التواصل معنا إذا لم تتلقى الرسالة في غضو دقائق."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"تتطلب هذه الصفحة توثيق هويتك.\n"
"لهذا نطلب منك توثيق ملكية\n"
" بريدك الإلكتروني. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"أرسلنا لك رسالة للتوثيق.\n"
"يرجى الضغط على الرابط في الرسالة. إذا لم ترى الرسالة في صندوقك الرئيسي، تحقق "
"من صندوق الرسائل غير المرغوب فيها.\n"
"يرجى التواصل معنا إذا لم تتلقاه في غضون دقائق."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>ملاحظة:</strong> ما زال بإمكانك <a href=\"%(email_url)s\">تغيير "
"البريد الإلكتروني</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "رسائل:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "قائمة:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "اتصالات الحساب"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "توثيق ذو عاملين"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "الجلسات"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr "حسابك محمي بواسطة المصادقة الثنائية. الرجاء إدخال رمز المصادقة:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "تم إنشاء مجموعة جديدة من رموز استرداد المصادقة الثنائية."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "تم إنشاء رموز استرداد جديدة"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "تم تفعيل تطبيق المصادقة."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "تم تفعيل تطبيق المصادقة"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "تم تعطيل تطبيق المصادقة."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "تم تعطيل تطبيق المصادقة"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "أضيف مفتاح أمان جديد."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "تم إضافة مفتاح أمان"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "تم حذف مفتاح أمان."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "تم حذف مفتاح أمان"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "تطبيق المصادقة"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "المصادقة باستخدام تطبيق المصادقة نشطة."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "تطبيق المصادقة غير نشط."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "إلغاء التنشيط"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "تفعيل"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "مفاتيح أمان"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "أضفت %(count)s مفتاح أمان."
msgstr[1] "أضفت مفتاح أمان واحد."
msgstr[2] "أضفت مفتاحين أمان."
msgstr[3] "أضفت %(count)s مفاتيح أمان."
msgstr[4] "أضفت %(count)s مفتاح أمان."
msgstr[5] "أضفت %(count)s مفتاح أمان."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "لم تُضاف مفاتيح أمان."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "إدارة"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "إضافة"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "رموز الاسترداد"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "هناك 6(unused_count)s من %(total_count)s رموز الاسترداد المتاحة."
msgstr[1] "هناك 6(unused_count)s من %(total_count)s رموز الاسترداد المتاحة."
msgstr[2] "هناك %(unused_count)s من %(total_count)s رموز الاسترداد المتاحة."
msgstr[3] "هناك %(unused_count)s من %(total_count)s رموز الاسترداد المتاحة."
msgstr[4] "هناك %(unused_count)s من %(total_count)s رموز الاسترداد المتاحة."
msgstr[5] "هناك %(unused_count)s من %(total_count)s رموز الاسترداد المتاحة."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "لم يتم إعداد رموز الاسترداد."

#: templates/mfa/index.html:96
msgid "View"
msgstr "عرض"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "تحميل"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "يولد"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "تم إنشاء مجموعة جديدة من رموز الاسترداد."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "تم إضافة مفتاح أمان."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "تم حذف مفتاح أمان."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "أدخل كود تطبيق المصادقة:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "أنت على وشك إنشاء مجموعة جديدة من رموز الاسترداد لحسابك."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "سيؤدي هذا الإجراء إلى إبطال رموزك الحالية."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "هل أنت متأكد؟"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "رموز غير مستخدمة"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "تنزيل الرموز"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "توليد رموز جديدة"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "تفعيل تطبيق المصادقة"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"لحماية حسابك من خلال المصادقة الثنائية، قم بمسح رمز الاستجابة السريعة أدناه "
"باستخدام تطبيق المصادقة الخاص بك. ثم أدخل رمز التحقق الذي أنشأه التطبيق "
"أدناه."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "سر تطبيق المصادقة"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"يمكنك تخزين هذا السر واستخدامه لإعادة تثبيت تطبيق المصادقة الخاص بك في وقت "
"لاحق."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "قم بإلغاء تنشيط تطبيق المصادقة"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"أنت على وشك إلغاء تنشيط المصادقة المستندة إلى تطبيق المصادقة. هل أنت متأكد؟"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "هل تثق بهذا المتصفح؟"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""
"إذا اخترت الوثوق بهذا المتصفح، فلن يُطلب منك رمز التحقق في المرة التالية التي "
"تقوم فيها بتسجيل الدخول."

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "الثقة لفترة %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "لا تثق"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "إضافة مفتاح أمان"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "إزالة مفتاح أمان"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "هل أنت متأكد أنك تريد إزالة مفتاح الأمان هذا؟"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "الاستخدام"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "مفتاح المرور"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "مفتاح أمان"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "هذا المفتاح لا يشير إلى ما إذا كان مفتاح المرور."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "غير محدد"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "تاريخ الإضافة: %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "تاريخ آخر استعمال: %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "تحرير"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "تغيير مفتاح الأمان"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "حفظ"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "إنشاء مفتاح مرور"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"أنت على وشك أن تنشأ مفتاح مرور لحسابك. يمكنك تعيين اسم وصفي لهذا المفتاح "
"لتمييزه عن أي مفتاح مرور تضيفه لاحقا."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "إنشاء"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "تحتاج هذه العملية استعمال جافاسكرِبت."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "فشل تسجيل الدخول بحساب طرف ثالث"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "حدث خطأ أثناء محاولة تسجيل الدخول عن طريق حساب طرف ثالث."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"يمكنك تسجيل الدخول إلى حسابك باستخدام أي من حسابات الطرف الثالث التالية:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "ليس لديك حاليا أي حساب طرف ثالث متصل بهذا الحساب."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "إضافة حساب طرف ثالث"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "تم ربط حساب جهة خارجية من %(provider)s بحسابك."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "تم إضافة حساب طرف ثالث"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "تم قطع اتصال حساب جهة خارجية من %(provider)s بحسابك."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "تم إزالة حساب طرف ثالث"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "ربط %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "أنت على وشك ربط حساب طرف ثالث جديد من %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "تسجيل الدخول عبر %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "أنت على وشك تسجيل الدخول باستخدام حساب طرف ثالث من %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "المتابعة"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "تم إلغاء تسجيل الدخول"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"قد قررت إلغاء تسجيل الدخول إلى الموقع باستخدام أحد الحسابات الموجودة الخاصة "
"بك. إذا كان هذا خطأ، الرجاء المتابعة إلى <a href=\"%(login_url)s\">تسجيل "
"الدخول</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "تم ربط حساب طرف ثالث."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "تم قطع الاتصال بحساب الطرف الثالث."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"أنت على وشك استخدام حسابك من %(provider_name)s لتسجيل الدخول إلى "
"%(site_name)s.\n"
"كخطوة أخيرة، يرجى ملء النموذج التالي:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "أو استخدم طرفًا ثالثًا"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "تم تسجيل الخروج من جميع الجلسات الأخرى."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "بدأت في"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "عنوان IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "المتصفح"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "شوهد آخر مرة في"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "حالي"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "تسجيل الخروج من الجلسات الأخرى"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "جلسات المستخدم"

#: usersessions/models.py:92
msgid "session key"
msgstr "مفتاح جلسة"

#, fuzzy
#~ msgid "Account Connection"
#~ msgstr "اتصالات الحساب"

#~ msgid "Use security key or device"
#~ msgstr "استخدم مفتاح او جهاز الحماية"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "كلمة المرور يجب أن لا تقل عن {0} حروف."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "تلقيت هذه الرسالة لأنك أو أحد آخر طلب كلمة لحسابك.\n"
#~ "لكن ليس لدينا قيد مستخدم يستخدم عنوان البريد الإلكتروني %(email)s\n"
#~ "في قاعدة بياناتنا.\n"
#~ "\n"
#~ "يمكنك تجاهل هذه الرسالة بأمان إذا لم تطلب إعادة ضبط كلمة المرور.\n"
#~ "\n"
#~ "إذا كنت أنت من طلب، يمكنك تسجيل حساب باستخدام الرابط في الأسفل."

#~ msgid "The following email address is associated with your account:"
#~ msgstr "عناوين البريد الإلكتروني التالية مربوطة مع حسابك:"

#~ msgid "Change Email Address"
#~ msgstr "تغيير البريد الإلكتروني"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "يرجى تسجيل الدخول مع واحد من حسابات الطرف الثالث الموجودة لديك‪.‬\n"
#~ "كما يمكنك <a href=\"%(signup_url)s\">تسجيل</a> حساب\n"
#~ "في موقع %(site_name)s والدخول في الأسفل:"

#~ msgid "or"
#~ msgstr "أو"

#~ msgid "change password"
#~ msgstr "تغيير كلمة المرور"

#~ msgid "OpenID Sign In"
#~ msgstr "تسجيل الدخول عبر OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "عنوان البريد الإلكتروني هذا مقترن بالفعل بحساب آخر."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "لقد قمنا بإرسال رسالة اليك عبر البريد الإلكتروني. يرجى الاتصال بنا اذا "
#~ "كنت لا تتلقى في غضون بضع دقائق."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "تسجيل الدخول و / أو كلمة المرور الذي حددته غير صحيحة."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "يمكن أن تحتوي أسماء المستخدمين إلا على الحروف، الأرقام و @/‪.‬/-/+/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "اسم المستخدم مسجل مسبقا. الرجاء اختيار اسم اخر‪.‬"

#, fuzzy
#~ msgid "Shopify Sign In"
#~ msgstr "تسجيل الدخول Shopify"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "لقد اكّدت ان <a href=\"mailto:%(email)s\">%(email)s</a> هو من إحدى عناوين "
#~ "للمستعمل %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "شكرا لاستخدام موقعنا!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "ارسلت رسالة التأكيد الى بريدك الالكتروني %(email)s"

#~ msgid "Delete Password"
#~ msgstr "احذف كلمة المرور"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "بامكانك حذف كلمة المرور بما انك قمت بتسجيل الدخول بواسطة OpenID"

#~ msgid "delete my password"
#~ msgstr "احذف كلمة المرور الخاصة بي"

#~ msgid "Password Deleted"
#~ msgstr "تم حذف كلمة المرور"
