# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <PERSON><PERSON>, 2013-2014
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-03-14 11:46+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <https://hosted.weblate.org/projects/allauth/django-"
"allauth/de/>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.7.1-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Dieses <PERSON> ist derzeit inaktiv."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Du kannst deine primäre E-Mail-Adresse nicht löschen."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Diese E-Mail-Adresse wird bereits in diesem Konto verwendet."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Die E-Mail-Adresse und/oder das Passwort sind leider falsch."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Die angegebene Telefonnummer und/oder das Passwort sind leider falsch."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Es ist bereits jemand mit dieser E-Mail-Adresse registriert."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Bitte gib dein aktuelles Passwort ein."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Falscher Code."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Falsches Passwort."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Falsches oder abgelaufenes Token."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Ungültige Anmeldung."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Das Sicherheits-Token zum Zurücksetzen des Passwortes war ungültig."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Du kannst nicht mehr als %d E-Mail-Adressen hinzufügen."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Es ist bereits jemand mit dieser E-Mail-Adresse registriert."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Zu viele gescheiterte Anmeldeversuche. Bitte versuche es später erneut."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "Diese E-Mail-Adresse ist keinem Konto zugeordnet."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Die Telefonnummer ist keinem Benutzerkonto zugeordnet."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Deine primäre E-Mailadresse muss bestätigt werden."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Anmeldename kann nicht verwendet werden. Bitte wähle einen anderen Namen."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Der Anmeldename und/oder das Passwort sind leider falsch."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Bitte wähle nur eine aus."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Die neue Wert muss sich vom aktuellen unterscheiden."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Verwenden Sie Ihr Passwort"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Verwenden Sie eine Authentifizierungs-App oder einen Code"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Verwenden Sie einen Sicherheitsschlüssel"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Markierte E-Mail-Adressen als verifiziert kennzeichnen"

#: account/apps.py:11
msgid "Accounts"
msgstr "Konten"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Geben Sie eine Telefonnummer einschließlich Ländervorwahl ein (z. B. +49 für "
"Deutschland)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Telefon"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Du musst zweimal das selbe Passwort eingeben."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Passwort"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Angemeldet bleiben"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "E-Mail-Adresse"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-Mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Anmeldename"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Anmeldung"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Anmeldename, E-Mail oder Telefon"

#: account/forms.py:156
msgid "Username or email"
msgstr "Anmeldename oder E-Mail"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Anmeldename oder Telefon"

#: account/forms.py:160
msgid "Email or phone"
msgstr "E-Mail oder Telefon"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Passwort vergessen?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-Mail (wiederholen)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Bestätigung der E-Mail-Adresse"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-Mail (optional)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Anmeldename (optional)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Du musst zweimal dieselbe E-Mail-Adresse eingeben."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Passwort (Wiederholung)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Aktuelles Passwort"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Neues Passwort"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Neues Passwort (Wiederholung)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Code"

#: account/models.py:26
msgid "user"
msgstr "Benutzer"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "E-Mail-Adresse"

#: account/models.py:34
msgid "verified"
msgstr "bestätigt"

#: account/models.py:35
msgid "primary"
msgstr "Primär"

#: account/models.py:41
msgid "email addresses"
msgstr "E-Mail-Adressen"

#: account/models.py:151
msgid "created"
msgstr "Erstellt"

#: account/models.py:152
msgid "sent"
msgstr "Gesendet"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "Schlüssel"

#: account/models.py:158
msgid "email confirmation"
msgstr "E-Mail-Bestätigung"

#: account/models.py:159
msgid "email confirmations"
msgstr "E-Mail-Bestätigungen"

#: headless/apps.py:7
msgid "Headless"
msgstr "Kopflos"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Sie können keine E-Mail-Adresse zu einem Konto hinzufügen, das durch die "
"Zwei-Faktor-Authentifizierung geschützt ist."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Sie können die Zwei-Faktor-Authentifizierung nicht deaktivieren."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Sie können keine Wiederherstellungscodes generieren, ohne die Zwei-Faktor-"
"Authentifizierung aktiviert zu haben."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Sie können die Zwei-Faktor-Authentifizierung nicht aktivieren, bis Sie Ihre "
"E-Mail-Adresse verifiziert haben."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Hauptschlüssel"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Sicherungsschlüssel"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Schlüsselnr. {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Wiederherstellungs-Codes"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP Authenticator"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Authentifizierungscode"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Passwortlos"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Wenn Sie den passwortlosen Betrieb aktivieren, können Sie sich nur mit "
"diesem Schlüssel anmelden, müssen aber zusätzliche Anforderungen erfüllen, "
"wie z. B. biometrische Daten oder PIN-Schutz."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Es existiert bereits ein Konto mit dieser E-Mail-Adresse. Bitte melde dich "
"zuerst mit diesem Konto an, und verknüpfe es dann mit deinem %s-Konto."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Falsches Token."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Für dein Konto wurde noch kein Passwort festgelegt."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Dein Konto hat keine bestätigte E-Mail-Adresse."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Sie können Ihr letztes verbleibendes Drittanbieterkonto nicht trennen."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr ""
"Das Konto des Drittanbieters ist bereits mit einem anderen Konto dieser "
"Seite verknüpft."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Konto"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "Anbieter"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "Anbieter-ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "Name"

#: socialaccount/models.py:58
msgid "client id"
msgstr "Client-ID"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "App-ID oder 'Consumer key'"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "Geheimer Schlüssel"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "'API secret', 'client secret' oder 'consumer secret'"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Schlüssel"

#: socialaccount/models.py:81
msgid "social application"
msgstr "Soziale Anwendung"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "Soziale Anwendungen"

#: socialaccount/models.py:117
msgid "uid"
msgstr "UID"

#: socialaccount/models.py:119
msgid "last login"
msgstr "Letzte Anmeldung"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "Registrierdatum"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "Weitere Daten"

#: socialaccount/models.py:125
msgid "social account"
msgstr "Soziales Konto"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "Soziale Konten"

#: socialaccount/models.py:160
msgid "token"
msgstr "Token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) oder \"access token\" (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "Geheimes Token"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) oder \"refresh token\" (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "Läuft ab"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "Token für soziale Anwendung"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "Tokens für soziale Anwendungen"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Ungültige Profildaten"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Anmeldung"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Abbrechen"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Ungültige Antwort von \"%s\" als Anfrageschlüssel erbeten wurde. Die Antwort "
"war: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Ungültige Antwort von \"%s\" als Zugangsschlüssel erbeten wurde."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Kein Request-Token gespeichert für \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Kein Access-Token gespeichert für \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Kein Zugriff zu privaten Daten auf \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Ungültige Antwort von \"%s\" als Anfrageschlüssel erbeten wurde."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Konto inaktiv"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Dieses Konto ist inaktiv."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Wir haben einen Code an %(recipient)s gesendet. Der Code läuft in Kürze ab, "
"bitte geben Sie ihn bald ein."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Bestätigen"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Code anfordern"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Zugriff bestätigen"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Bitte authentifizieren Sie sich erneut, um Ihr Konto zu schützen."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Alternative Optionen"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "E-Mail-Verifizierung"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Geben Sie den E-Mail-Bestätigungscode ein"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "E-Mail-Adresse"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Anmeldung"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Anmeldecode eingeben"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Passwort zurücksetzen"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Passwort-Zurücksetzungscode Eingeben"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Telefonverifizierung"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Geben Sie den Telefon-Bestätigungscode ein"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-Mail-Adressen"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Folgende E-Mail-Adressen sind mit diesem Konto verknüpft:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Bestätigt"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Unbestätigt"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Primär"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Als primäre Adresse festlegen"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Bestätigungs-Mail nochmal verschicken"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Ausgewählte entfernen"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "E-Mail-Adresse hinzufügen"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "E-Mail hinzufügen"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Möchtest du wirklich die ausgewählte E-Mail-Adresse entfernen?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Du erhältst diese E-Mail, weil du oder jemand anderes versucht hat, sich für "
"ein Konto anzumelden mit E-Mail-Adresse:\n"
"\n"
"%(email)s\n"
"\n"
"Es existiert jedoch bereits ein Konto mit dieser E-Mail-Adresse. Falls du "
"dies vergessen hast, verwende bitte das Passwort-Vergessen-Verfahren, um "
"dein Konto wiederherzustellen:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Konto existiert bereits"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Hallo von %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Danke dass du %(site_name)s nutzt!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Sie erhalten diese E-Mail, weil die folgende Änderung an Ihrem Konto "
"vorgenommen wurde:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Wenn Sie diese Änderung nicht erkennen, ergreifen Sie bitte umgehend "
"angemessene Sicherheitsmaßnahmen. Die Änderung an Ihrem Konto stammt von:\n"
"\n"
"- IP-Adresse: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Datum: %(timestamp)s\""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Ihre E-Mail wurde von %(from_email)s auf %(to_email)s geändert."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "E-Mail geändert"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Ihre E-Mail wurde bestätigt."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "E-Mail-Bestätigung"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Sie erhalten diese E-Mail, weil Benutzer %(user_display)s Ihre E-Mail-"
"Adresse angegeben hat, um ein Konto bei %(site_domain)s zu registrieren."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ihr E-Mail-Verifizierungscode ist unten aufgeführt. Bitte geben Sie diese in "
"Ihrem geöffneten Browserfenster ein."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Um zu bestätigen, dass dies korrekt ist, gehen Sie zu %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Bitte bestätige deine E-Mail-Adresse"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "Die E-Mail-Adresse %(deleted_email)s wurde aus Ihrem Konto entfernt."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "E-Mail entfernt"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Ihr Anmeldecode steht unten. Bitte geben Sie ihn in Ihrem geöffneten "
"Browserfenster ein."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Diese E-Mail kann ignoriert werden, wenn Sie diese Aktion nicht initiiert "
"haben."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Anmeldecode"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Dein Passwort wurde geändert."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Passwort geändert"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ihre Passwort-Zurücksetzungscode steht unten. Bitte geben Sie ihn in Ihrem "
"geöffneten Browserfenster ein."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Passwort-Zurücksetzungscode"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Du erhältst diese E-Mail weil du oder jemand anderes die Zurücksetzung des "
"Passwortes für dein Konto gefordert hat.\n"
"Falls es sich dabei nicht um dich handelt, kann diese Nachricht ignoriert "
"werden. Rufe folgende Adresse auf um dein Passwort zurückzusetzen."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr ""
"Falls du deinen Anmeldenamen vergessen haben solltest; er lautet "
"%(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "E-Mail zum Zurücksetzen des Passworts"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Ihr Passwort wurde zurückgesetzt."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Ihr Passwort wurde festgelegt."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Passwort festgelegt"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Sie erhalten diese E-Mail, weil Sie oder jemand anderes versucht hat, auf "
"ein Konto mit der E-Mail %(email)s zuzugreifen. Wir haben jedoch keinen "
"solchen Account in unserer Datenbank gefunden."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Wenn es von Ihnen war, können Sie über den unten stehenden Link ein Konto "
"erstellen."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Unbekanntes Konto"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Email-Adresse"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Aktuelle E-Mail"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Wechsel zu"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Deine E-Mail-Adresse steht immer noch aus und muss überprüft werden."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Änderung abbrechen"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Ändern zu"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Email ändern"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "E-Mail-Adresse bestätigen"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Bitte bestätige, dass <a href=\"mailto:%(email)s\">%(email)s</a> eine E-Mail-"
"Adresse von %(user_display)s ist."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Kann %(email)s nicht bestätigen, da sie bereits von einem anderen Konto "
"bestätigt wurde."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Dieser Bestätigungslink ist leider abgelaufen. Lass Dir bitte eine neue <a "
"href=\"%(email_url)s\">Bestätigungs-Mail</a> schicken."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Du hast noch kein Konto bei uns? Dann %(link)serstelle%(end_link)s bitte "
"zunächst eins."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Mit einem Passkey anmelden"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Anmeldecode senden"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Abmelden"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Bist du sicher, dass du dich abmelden möchtest?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Du kannst deine primäre E-Mail-Adresse (%(email)s) nicht löschen."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Bestätigungs-E-Mail wurde an %(email)s verschickt."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Du hast die Adresse %(email)s bestätigt."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-Mailadresse %(email)s entfernt."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Erfolgreich als %(name)s angemeldet."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Du hast dich abgemeldet."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Ein Anmeldecode wurde an %(recipient)s gesendet."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Das Passwort wurde geändert."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Das Passwort wurde erfolgreich gesetzt."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Ein Anmeldecode wurde an %(recipient)s gesendet."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Sie haben die Telefonnummer %(phone)s verifiziert."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primäre E-Mailadresse festgelegt."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Passwort ändern"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Passwort vergessen?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Passwort vergessen? Gib deine E-Mail-Adresse unten ein, dann schicken wir "
"dir einen Link, unter dem du dein Passwort zurücksetzen kannst."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Passwort zurücksetzen"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Bitte kontaktiere uns, wenn das Zurücksetzen des Passworts nicht klappt."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Wir haben Dir eine E-Mail geschickt. Wenn du die E-Mail nicht in deinem "
"Posteingang siehst, überprüfe bitte deinen Spam-Ordner. Wenn die E-Mail "
"ansonsten nicht in ein paar Minuten angekommen ist, gib uns bitte Bescheid."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Falsches Token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Der Link zum Zurücksetzen des Passworts war ungültig, womöglich wurde dieser "
"Link bereits benutzt. Bitte lass dein Passwort noch mal <a "
"href=\"%(passwd_reset_url)s\">zurücksetzen</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Dein Passwort wurde geändert."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Passwort setzen"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Telefon ändern"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Aktuelle Telefonnummer"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Dein Telefonnummer steht immer noch aus und muss überprüft werden."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Geben Sie Ihr Passwort ein:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Sie erhalten einen speziellen Code für eine passwortfreie Anmeldung."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Code anfordern"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Andere Anmeldeoptionen"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registrieren"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Registrieren"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr ""
"Du hast bereits ein Konto bei uns? Dann bitte %(link)shier "
"entlang%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Mit einem Passkey registrieren"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Passkey Registrierung"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Andere Optionen"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registrierung geschlossen"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Es tut uns leid, aber die Registrierung ist derzeit geschlossen."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Anmerkung"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Du bist bereits als %(user_display)s angemeldet."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Warnung:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Du hast derzeit keine E-Mail-Adressen angegeben. Das solltest du allerdings "
"tun, denn nur so können wir dich benachrichtigen und dein Passwort "
"zurücksetzen."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Bestätige deine E-Mail-Adresse"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Wir haben dir eine E-Mail geschickt, um deine Adresse zu verifizieren. Bitte "
"folge dem Link in der E-Mail um den Anmeldeprozess abzuschließen. Wenn du "
"die E-Mail nicht in deinem Posteingang siehst, überprüfe bitte deinen Spam-"
"Ordner. Wenn die E-Mail nicht in ein paar Minuten angekommen ist, gib uns "
"bitte Bescheid."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Auf diesem Teil der Webseite möchten wie sichergehen,\n"
"dass du die Person bist für die du dich ausgibst.\n"
"Dazu musst du deine E-Mail-Adresse verifizieren. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Wir haben Dir eine E-Mail geschickt, um deine\n"
"Adresse zu verifizieren. Bitte klick auf den Link\n"
"in der E-Mail. Wenn du die E-Mail nicht in deinem Posteingang siehst, "
"überprüfe bitte deinen Spam-Ordner. Wenn die E-Mail ansonsten nicht in ein "
"paar Minuten angekommen ist, gib uns bitte Bescheid."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Anmerkung:</strong> Du kannst <a href=\"%(email_url)s\">Deine E-Mail-"
"Adresse ändern</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Nachrichten:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menü"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Konto-Verknüpfungen"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Zwei-Faktor-Authentifizierung"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sitzungen"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Ihr Konto ist durch Zwei-Faktor-Authentifizierung geschützt. Bitte geben Sie "
"einen Authenticator-Code ein:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "Ein neuer Satz Wiederherstellungscodes wurde generiert."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Neue Wiederherstellungscodes generiert"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Authenticator-App aktiviert."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Authenticator-App aktiviert"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Authenticator-App deaktiviert."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Authenticator-App deaktiviert"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Es wurde ein neuer Sicherheitsschlüssel hinzugefügt."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Sicherheitsschlüssel hinzugefügt"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Ein Sicherheitsschlüssel wurde entfernt."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Sicherheitsschlüssel entfernt"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Authenticator-App"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Die Authentifizierung mit einer Authenticator-App ist aktiv."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Es ist keine Authenticator-App aktiv."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deaktivieren"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Aktivieren"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Sicherheitsschlüssel"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Sie haben %(count)s Sicherheitsschlüssel hinzugefügt."
msgstr[1] "Sie haben %(count)s Sicherheitsschlüssel hinzugefügt."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Es wurden keine Sicherheitsschlüssel hinzugefügt."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Verwalten"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Hinzufügen"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Wiederherstellungs-Codes"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Es steht %(unused_count)s von %(total_count)s Wiederherstellungscodes zur "
"Verfügung."
msgstr[1] ""
"Es stehen %(unused_count)s von %(total_count)s Wiederherstellungscodes zur "
"Verfügung."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Es wurden keine Wiederherstellungscodes eingerichtet."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Anzeigen"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Herunterladen"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generieren"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Ein neuer Satz Wiederherstellungscodes wurde generiert."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Sicherheitsschlüssel hinzugefügt."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Sicherheitsschlüssel entfernt."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Geben Sie einen Authentifizierungscode ein:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Du bist dabei, einen neuen Satz Wiederherstellungscodes für Ihr Konto zu "
"generieren."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Diese Aktion wird Ihre vorhandenen Codes ungültig machen."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Sind Sie sicher?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Unbenutzte Codes"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Codes herunterladen"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Neue Codes generieren"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Authenticator-App aktivieren"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Um Ihr Konto mit der Zwei-Faktor-Authentifizierung zu schützen, scannen Sie "
"den unten stehenden QR-Code mit Ihrer Authenticator-App. Geben Sie dann den "
"von der App generierten Bestätigungscode unten ein."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Authentifizierungsgeheimnis"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Sie können dieses Geheimnis speichern und zu einem späteren Zeitpunkt "
"verwenden, um Ihre Authenticator-App neu zu installieren."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Authenticator-App deaktivieren"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Du bist dabei, die Authentifizierung per Authenticator-App zu deaktivieren. "
"Sind Sie sicher?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Sicherheitsschlüssel hinzufügen"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Sicherheitsschlüssel entfernen"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr ""
"Sind Sie sicher, dass Sie diesen Sicherheitsschlüssel entfernen möchten?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Verwendung"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Passkey"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Sicherheitsschlüssel"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Dieser Schlüssel gibt nicht an, ob es sich um einen Passkey handelt."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Unbestimmt"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Hinzugefügt um %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Zuletzt verwendet %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Bearbeiten"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Sicherheitsschlüssel bearbeiten"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Speichern"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Passkey erstellen"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Sie sind dabei, einen Passkey für Ihr Konto zu erstellen. Da Sie später "
"weitere Schlüssel hinzufügen können, können Sie einen beschreibenden Namen "
"verwenden, um die Schlüssel voneinander zu unterscheiden."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Erstellen"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Diese Funktionalität erfordert JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Drittanbieter-Anmeldefehler"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Beim Versuch, sich über Ihr Drittanbieterkonto anzumelden, ist ein Fehler "
"aufgetreten."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Sie können sich bei Ihrem Konto mit einem der folgenden Drittanbieter-Konten "
"anmelden:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Derzeit sind keine Drittanbieterkonten mit diesem Konto verbunden."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Einen Drittanbieter-Account hinzufügen"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"Ein Drittanbieterkonto von %(provider)s wurde mit Ihrem Konto verbunden."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Drittanbieterkonto verbunden"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"Ein Drittanbieterkonto von %(provider)s wurde von Ihrem Konto getrennt."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Drittanbieterkonto getrennt"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Mit %(provider)s verbinden"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Du bist dabei, ein neues Drittanbieter-Konto von %(provider)s zu verknüpfen."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Anmelden über %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Du bist dabei, dich mit einem Konto von %(provider)s anzumelden."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Fortfahren"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Anmeldung abgebrochen"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Du hast die Anmeldung abgebrochen. Wenn das nur ein Versehen oder ein Fehler "
"war, folge bitte diesem <a href=\"%(login_url)s\">Link</a> um dich "
"anzumelden."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Konten wurden erfolgreich verknüpft."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Das Drittanbieterkonto wurde getrennt."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Du verwendest dein %(provider_name)s-Konto, um dich bei\n"
"%(site_name)s anzumelden. Zum Abschluss bitte das folgende Formular "
"ausfüllen:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Oder Drittanbieter verwenden"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Von allen anderen Sitzungen abgemeldet."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Gestartet um"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP-Adresse"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Browser"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Zuletzt gesehen um"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Aktuell"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Andere Sitzungen abmelden"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Benutzersitzungen"

#: usersessions/models.py:92
msgid "session key"
msgstr "Sitzungsschlüssel"

#~ msgid "Account Connection"
#~ msgstr "Konto-Verknüpfung"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Das Passwort muss aus mindestens {0} Zeichen bestehen."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Du erhältst diese E-Mail weil du oder jemand anderes die Zurücksetzung "
#~ "des Passwortes für dein Konto gefordert hat. Wir haben jedoch keinen "
#~ "Eintrag eines Benutzers mit der E-Mail-Adresse %(email)s in unserer "
#~ "Datenbank.\n"
#~ "\n"
#~ "Falls es sich dabei nicht um dich handelt, kann diese Nachricht ignoriert "
#~ "werden.\n"
#~ "\n"
#~ "Ansonsten kannst du dich über den unten stehenden Link für ein Konto "
#~ "anmelden."

#~ msgid "The following email address is associated with your account:"
#~ msgstr "Die folgende Email-Adresse ist mit Ihrem Konto verknüpft:"

#~ msgid "Change Email Address"
#~ msgstr "E-Mail-Adresse ändern"

#~ msgid ""
#~ "To safeguard the security of your account, please enter your password:"
#~ msgstr ""
#~ "Um die Sicherheit Ihres Kontos zu gewährleisten, geben Sie bitte Ihr "
#~ "Passwort ein:"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Bitte melde dich mit einem der folgenden Netzwerkkonten an, oder  <a \n"
#~ "href=\"%(signup_url)s\">registriere dich auf %(site_name)s</a>, dann "
#~ "kannst du dich unten mit deinem Konto anmelden:"

#~ msgid "or"
#~ msgstr "oder"

#~ msgid "change password"
#~ msgstr "Passwort ändern"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID-Anmeldung"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Diese E-Mail-Adresse wird bereits in einem anderen Konto verwendet."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Wir haben Dir eine E-Mail geschickt. Bitte kontaktiere uns, wenn du sie "
#~ "nicht in ein paar Minuten erhalten hast."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Die Anmeldedaten sind leider falsch."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Anmeldenamen dürfen nur Buchstaben und Ziffern und folgende Zeichen "
#~ "enthalten: @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Der Anmeldename ist bereits vergeben – bitte wähle einen anderen."

#, fuzzy
#~ msgid "Shopify Sign In"
#~ msgstr "Anmeldung"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Du hast bestätigt, dass <a href=\"mailto:%(email)s\">%(email)s</a> eine "
#~ "gültige Adresse von %(user_display)s ist."

#~ msgid "Thanks for using our site!"
#~ msgstr "Danke, dass du unsere Seite nutzt!"
