<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}YalaOffice{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: #fff;
            background: #007bff;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .navbar-brand {
            font-weight: bold;
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user.is_authenticated %}
        <!-- Sidebar -->
        <nav class="sidebar position-fixed d-none d-md-block">
            <div class="p-3">
                <a href="{% url 'home' %}" class="navbar-brand text-white text-decoration-none">
                    <i class="fas fa-boxes"></i> YalaOffice
                </a>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                
                {% if user.is_admin or user.is_manager %}
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'accounts:user_list' %}">
                        <i class="fas fa-users"></i> Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'accounts:branch_list' %}">
                        <i class="fas fa-store"></i> Branches
                    </a>
                </li>
                {% endif %}
                
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-boxes"></i> Inventory
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-shopping-cart"></i> Orders
                    </a>
                </li>
                
                {% if user.is_delivery %}
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-truck"></i> Deliveries
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_admin or user.is_manager %}
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                </li>
                {% endif %}
                
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'accounts:profile' %}">
                        <i class="fas fa-user"></i> Profile
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark main-content">
            <div class="container-fluid">
                <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="ms-auto">
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            {% if user.profile.avatar %}
                                <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="user-avatar me-2">
                            {% else %}
                                <i class="fas fa-user-circle me-2"></i>
                            {% endif %}
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                <i class="fas fa-user"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'account_logout' %}">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
    {% else %}
        <!-- Public Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="{% url 'home' %}">
                    <i class="fas fa-boxes"></i> YalaOffice
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{% url 'account_login' %}">Login</a>
                    <a class="nav-link" href="{% url 'account_signup' %}">Sign Up</a>
                </div>
            </div>
        </nav>
    {% endif %}

    <!-- Main Content -->
    <main class="{% if user.is_authenticated %}main-content{% endif %}">
        <div class="container-fluid p-4">
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Page Content -->
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
