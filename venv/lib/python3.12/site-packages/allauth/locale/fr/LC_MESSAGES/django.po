# DJANGO-ALLAUTH.
# Copyright (C) 2016
# This file is distributed under the same license as the django-allauth package.
#
# Translators:
# <PERSON> <steve.koss<PERSON><PERSON>@yahoo.fr>, 2016.
# <PERSON><PERSON> <<EMAIL>>, 2019
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-03-14 11:46+0100\n"
"Last-Translator: Laurent FAVOLE <<EMAIL>>\n"
"Language-Team: French <https://hosted.weblate.org/projects/allauth/django-"
"allauth/fr/>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.8-rc\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Ce compte est actuellement désactivé."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Vous ne pouvez pas retirer votre adresse e-mail principale."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "L'adresse e-mail est déjà associée à votre compte."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "L’adresse e-mail ou le mot de passe sont incorrects."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr ""
"Le numéro de téléphone et/ou le mot de passe que vous avez spécifié ne sont "
"pas corrects."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Un autre utilisateur utilise déjà cette adresse e-mail."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Merci d'indiquer votre mot de passe actuel."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Code incorrect."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Mot de passe incorrect."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Clé invalide ou expirée."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Identifiant invalide."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Le jeton de réinitialisation de mot de passe est invalide."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Vous ne pouvez pas ajouter plus de %d adresses e-mail."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Un autre utilisateur utilise déjà cette adresse e-mail."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Trop de tentatives de connexion échouées. Veuillez réessayer ultérieurement."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "Cette adresse e-mail n'est pas associée à un compte utilisateur."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Le numéro de téléphone n'est associé à aucun compte utilisateur."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Votre adresse e-mail principale doit être vérifiée."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Ce pseudonyme ne peut pas être utilisé. Veuillez en choisir un autre."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Le pseudo et/ou le mot de passe sont incorrects."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Veuillez en sélectionner un seul."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "La nouvelle valeur doit être différente de l'actuelle."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Utilisez votre mot de passe"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Utilisez une application d'authentification ou un code"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Utilisez une clé secrète"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Marquer les adresses e-mail sélectionnées comme vérifiées"

#: account/apps.py:11
msgid "Accounts"
msgstr "Comptes"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Entrez un numéro de téléphone avec l'indicatif du pays (par exemple, +33 "
"pour la France)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Téléphone"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Vous devez saisir deux fois le même mot de passe."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Mot de passe"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Se souvenir de moi"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Adresse e-mail"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Nom d'utilisateur"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Identifiant"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Nom d'utilisateur, email ou téléphone"

#: account/forms.py:156
msgid "Username or email"
msgstr "Nom d'utilisateur ou e-mail"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Nom d'utilisateur ou téléphone"

#: account/forms.py:160
msgid "Email or phone"
msgstr "Email ou téléphone"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Mot de passe oublié ?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-mail (confirmation)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Confirmation d'adresse e-mail"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-mail (facultatif)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Nom d'utilisateur (facultatif)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Vous devez saisir deux fois le même e-mail."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Mot de passe (confirmation)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Mot de passe actuel"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nouveau mot de passe"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nouveau mot de passe (confirmation)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Code"

#: account/models.py:26
msgid "user"
msgstr "utilisateur"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "adresse e-mail"

#: account/models.py:34
msgid "verified"
msgstr "vérifiée"

#: account/models.py:35
msgid "primary"
msgstr "principale"

#: account/models.py:41
msgid "email addresses"
msgstr "adresses e-mail"

#: account/models.py:151
msgid "created"
msgstr "créé"

#: account/models.py:152
msgid "sent"
msgstr "envoyé"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "clé"

#: account/models.py:158
msgid "email confirmation"
msgstr "confirmation par e-mail"

#: account/models.py:159
msgid "email confirmations"
msgstr "confirmations par e-mail"

#: headless/apps.py:7
msgid "Headless"
msgstr "Headless"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Vous ne pouvez pas ajouter une adresse e-mail à un compte protégé par "
"l'authentification à deux facteurs."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Vous ne pouvez pas désactiver l'authentification à deux facteurs."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Vous ne pouvez pas générer de codes de récupération sans avoir activé "
"l'authentification à deux facteurs."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Vous ne pouvez pas activer l'authentification à deux facteurs tant que vous "
"n'avez pas vérifié votre adresse e-mail."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Clé principale"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Clé de secours"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Clé n°{number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Codes de récupération"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Authentificateur TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Code de l'authentificateur"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Sans mot de passe"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"L'activation des opérations sans mot de passe vous permet de vous connecter "
"en utilisant seulement cette clé/appareil, mais demande des exigences plus "
"élevées comme les informations biométriques ou un code PIN."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Un compte existe déjà avec cette adresse e-mail. Merci de vous connecter au "
"préalable avec ce compte, et ensuite connecter votre compte %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Mauvais jeton d'identification."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Vous devez d'abord définir le mot de passe de votre compte."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Vous devez d'abord associer une adresse e-mail à votre compte."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Vous ne pouvez pas déconnecter votre dernier compte tiers restant."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Ce compte social est déjà connecté à un autre compte."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Comptes sociaux"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "fournisseur"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID du fournisseur"

#: socialaccount/models.py:56
msgid "name"
msgstr "nom"

#: socialaccount/models.py:58
msgid "client id"
msgstr "id client"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "ID de l'app ou clé de l'utilisateur"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "clé secrète"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "Secret de l'API, secret du client, ou secret de l'utilisateur"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Clé"

#: socialaccount/models.py:81
msgid "social application"
msgstr "application sociale"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "applications sociales"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "dernière connexion"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "date d'inscription"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "données supplémentaires"

#: socialaccount/models.py:125
msgid "social account"
msgstr "compte social"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "comptes sociaux"

#: socialaccount/models.py:160
msgid "token"
msgstr "jeton"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ou jeton d'accès (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "jeton secret"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ou jeton d'actualisation (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "expire le"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "jeton de l'application sociale"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "jetons de l'application sociale"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Données de profil incorrectes"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Identifiant"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Annuler"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Réponse invalide lors de l'obtention du jeton de requête de \"%s\". La "
"réponse était : %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Réponse invalide lors de l'obtention du jeton d'accès depuis \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Aucun jeton de requête sauvegardé pour \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Aucun jeton d'accès sauvegardé pour \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Impossible d'accéder aux ressources privées de \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Réponse invalide lors de l'obtention du jeton de requête de \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Compte inactif"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Ce compte est inactif."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Nous avons envoyé un code à %(recipient)s. Le code expirera bientôt, "
"veuillez donc l'entrer rapidement."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Confirmer"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Demander un code"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Confirmer l'accès"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Veuillez vous réauthentifier pour protéger votre compte."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Options alternatives"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Vérification par e-mail"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Saisissez le code de vérification reçu par mail"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "adresse e-mail"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Connexion"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Entrez le code de connexion"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Réinitialisation du mot de passe"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Entrez le code de réinitialisation du mot de passe"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Vérification du téléphone"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Entrez le code de vérification du téléphone"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Adresses e-mail"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Les adresses e-mail suivantes sont associées à votre compte :"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Vérifiée"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Non vérifiée"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Principale"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Rendre principale"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Renvoyer le message de vérification"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Retirer"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Ajouter une adresse e-mail"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Ajouter un e-mail"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Voulez-vous vraiment retirer cette adresse e-mail ?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Vous recevez cet email car vous ou quelqu'un d'autre a demandé à créer\n"
"un compte en utilisant cette adresse e-mail :\n"
"\n"
"%(email)s\n"
"\n"
"Cependant, un compte utilisant cette adresse existe déjà. Au cas où vous "
"auriez\n"
"oublié, merci d'utiliser la fonction de récupération de mot de passe pour\n"
"récupérer votre compte :\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Ce compte existe déjà"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Bonjour, c'est %(site_name)s !"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Merci d'utiliser %(site_name)s !\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Vous recevez cet e-mail car la modification suivante a été apportée à votre "
"compte :"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Si vous ne reconnaissez pas ce changement, veuillez prendre immédiatement "
"les mesures de sécurité qui s'imposent. La modification de votre compte "
"provient de :\n"
"\n"
"- L'adresse IP : %(ip)s\n"
"- Navigateur : %(user_agent)s\n"
"- Date : %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Votre adresse e-mail a été modifiée de %(from_email)s à %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "E-mail modifié"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Votre e-mail a été confirmé."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Confirmation par e-mail"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Vous recevez cet e-mail car l'utilisateur %(user_display)s a indiqué votre "
"adresse pour se connecter à son compte sur %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Votre code de connexion est indiqué ci-dessous. Veuillez le saisir dans la "
"fenêtre ouverte de votre navigateur."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Pour confirmer que cela est correct, allez sur %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Confirmez votre adresse e-mail"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "L'adresse e-mail %(deleted_email)s a été retirée de votre compte."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "E-mail retiré"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Votre code de connexion est indiqué ci-dessous. Veuillez l'entrer dans votre "
"navigateur."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "Cet email peut être ignoré si vous n'avez rien effectué."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Code de connexion"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Votre mot de passe a été modifié."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Mot de passe modifié"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Votre code de réinitialisation du mot de passe est indiqué ci-dessous. "
"Veuillez l'entrer dans la fenêtre de votre navigateur ouverte."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Code de réinitialisation du mot de passe"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Vous recevez cet e-mail car vous ou quelqu'un d'autre a demandé une "
"réinitialisation de mot de passe pour votre compte utilisateur.\n"
"Vous pouvez simplement ignorer ce message si vous n'êtes pas à l'origine de "
"cette demande. Sinon, cliquez sur le lien ci-dessous pour réinitialiser "
"votre mot de passe."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr ""
"Au cas où vous l'auriez oublié, votre nom d'utilisateur est %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail de réinitialisation de mot de passe"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Votre mot de passe a été réinitialisé."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Votre mot de passe a été défini."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Définition du mot de passe"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Vous recevez cet email car vous, ou quelqu'un d'autre, a essayé d'accéder à "
"un compte avec l'email %(email)s. Cependant, nous ne disposons pas de ce "
"compte dans nos bases de données."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Si c'était vous, vous pouvez vous inscrire en utilisant le lien ci-dessous."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Compte inconnu"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Adresse e-mail"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "E-mail actuel"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Changer pour"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Votre adresse e-mail est toujours en attente de vérification."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Annuler la modification"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Changer pour"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Changer d'e-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Confirmer l'adresse e-mail"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Merci de confirmer que <a href=\"mailto:%(email)s\">%(email)s</a> est "
"l'adresse e-mail de %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Impossible de confirmer %(email)s car il est déjà confirmé par un autre "
"compte."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Ce lien de confirmation d'adresse e-mail a expiré ou n'est pas valide. "
"Veuillez lancer <a href=\"%(email_url)s\">une nouvelle demande de "
"confirmation</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Si vous n'avez pas encore créé de compte, merci de vous "
"%(link)senregistrer%(end_link)s au préalable."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Se connecter avec une clé d'accès"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Envoyez-moi un code de connexion"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Se déconnecter"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Êtes-vous sûr(e) de vouloir vous déconnecter ?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr ""
"Vous ne pouvez pas retirer votre adresse e-mail principale (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "E-mail de confirmation envoyé à %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Vous avez confirmé %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Adresse e-mail %(email)s retirée."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Connexion avec %(name)s réussie."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Vous êtes déconnecté(e)."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Un code de connexion a été envoyé à %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Mot de passe modifié avec succès."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Mot de passe défini avec succès."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Un code de connexion a été envoyé à %(recipient)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Vous avez vérifié le numéro de téléphone %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Adresse e-mail principale enregistrée."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Modifier le mot de passe"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Mot de passe oublié ?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Mot de passe oublié ? Indiquez votre adresse e-mail ci-dessous et nous vous "
"enverrons un e-mail pour le réinitialiser."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Réinitialiser mon mot de passe"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Merci de nous contacter si vous ne parvenez pas à réinitialiser votre mot de "
"passe."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Nous vous avons envoyé un e-mail. Si vous ne l'avez pas reçu, veuillez "
"vérifier votre dossier de spam. Sinon, contactez-nous si vous ne le recevez "
"pas dans quelques minutes."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Mauvais jeton d'identification"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Le lien de réinitialisation du mot de passe est invalide. Il a peut être "
"déjà été utilisé. Veuillez faire une nouvelle <a "
"href=\"%(passwd_reset_url)s\">demande de réinitialisation de mot de passe</"
"a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Votre mot de passe a été modifié."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Définir un mot de passe"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Changer de numéro de téléphone"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Numéro de téléphone actuel"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Votre numéro de téléphone est toujours en attente de vérification."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Saisissez votre mot de passe :"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Vous recevrez un code spécial pour une connexion sans mot de passe."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Demander un code"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Autres options de connexion"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Inscription"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Création de compte"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr ""
"Vous avez déjà un compte ? Vous pouvez donc %(link)svous "
"connecter%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "S'inscrire avec une clé d'accès"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Création de compte avec clé de sécurité"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Autres options"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Inscriptions fermées"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Nous sommes désolés, mais les inscriptions sont actuellement fermées."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Remarque"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Vous êtes déjà connecté en tant que %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Attention :"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Vous n'avez aucune adresse e-mail associée à votre compte. Vous devriez "
"ajouter une adresse e-mail pour pouvoir recevoir des notifications, "
"réinitialiser votre mot de passe, etc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Vérifiez votre adresse e-mail"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Nous vous avons envoyé un e-mail pour validation. Cliquez sur le lien fourni "
"dans l'e-mail pour terminer l'inscription. Si vous ne voyez pas l'e-mail de "
"vérification dans votre boîte de réception, vérifiez votre dossier spam. "
"Merci de nous contacter si vous ne le recevez pas d'ici quelques minutes."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Pour accéder à cette partie du site, il faut d'abord que\n"
"nous ayons vérifié qui vous indiquez être. Pour cela, vous devez prouver\n"
"que vous êtes bien le propriétaire de l'adresse e-mail que vous nous avez "
"indiquée. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Nous vous avons envoyé un e-mail de vérification.\n"
"Merci de cliquer sur le lien inclus dans ce courriel. Si vous ne voyez pas "
"l'e-mail de vérification dans votre boîte de réception, vérifiez votre "
"dossier spam.\n"
"Contactez-nous si vous ne l'avez pas reçu d'ici quelques minutes."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Remarque :</strong> vous pouvez toujours <a "
"href=\"%(email_url)s\">changer votre adresse e-mail</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Messages :"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu :"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Comptes associés"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Authentification à deux facteurs"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sessions"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Votre compte est protégé par l'authentification à deux facteurs. Veuillez "
"saisir un code d'authentification :"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Un nouveau jeu de codes de récupération pour l'authentification à deux "
"facteurs a été généré."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Nouveaux codes de récupération générés"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Application d'authentification activée."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Application d'authentification activée"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Application d'authentification désactivée."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Application d'authentification désactivée"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Une nouvelle clé de sécurité a été ajoutée."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Clé de sécurité ajoutée"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Une clé de sécurité a été retirée."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Clé de sécurité retirée"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Application d'authentification"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr ""
"L'authentification à l'aide d'une application d'authentification est active."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Aucune application d'authentification active."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Désactiver"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Activer"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Clés de sécurité"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Vous avez ajouté %(count)s clé de sécurité."
msgstr[1] "Vous avez ajouté %(count)s clés de sécurité."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Aucune clé de sécurité n'a été ajoutée."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Gérer"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Ajouter"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Codes de récupération"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Il y a %(unused_count)s code de récupération disponible sur %(total_count)s."
msgstr[1] ""
"Il y a %(unused_count)s codes de récupération disponibles sur "
"%(total_count)s."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Aucun code de récupération défini."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Afficher"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Télécharger"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Générer"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Un nouveau jeu de codes de récupération a été généré."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Clé de sécurité ajoutée."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Clé de sécurité enlevée."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Saisissez un code d'authentification :"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Vous êtes sur le point de générer un nouveau jeu de codes de récupération "
"pour votre compte."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Cette action invalidera vos codes existants."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Êtes-vous sûr(e) ?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Codes inutilisés"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Télécharger les codes"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Générer de nouveaux codes"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Activer l'application d'authentification"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Pour protéger votre compte avec l'authentification à deux facteurs, scannez "
"le code QR ci-dessous avec votre application d'authentification. Ensuite, "
"saisissez le code de vérification généré par l'application ci-dessous."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Secret d'authentification"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Vous pouvez stocker ce secret et l'utiliser pour réinstaller votre "
"application d'authentification ultérieurement."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Désactiver l'application d'authentification"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Vous êtes sur le point de désactiver l'authentification basée sur "
"l'application d'authentification. Êtes-vous sûr(e) ?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Ajouter une clé de sécurité"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Enlever une clé de sécurité"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Êtes-vous sûr de vouloir supprimer cette clé de sécurité ?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Utilisation"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Clé de sécurité"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "clé de sécurité"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Cette clé n'indique pas si c'est une clé de sécurité."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Non spécifiée"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Ajoutée à %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Dernière utilisation à %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Modifier"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Modifier la clé de sécurité"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Enregistrer"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Créer une clé de sécurité"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Vous allez créer une clé de sécurité pour votre compte. Étant donne que vous "
"pourrez en ajouter d'autres plus tard, vous pouvez utiliser un nom "
"descriptif pour différencier les clés."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Créer"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Cette fonctionnalité nécessite JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Échec de la connexion via réseau social"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Une erreur est survenue lors de la tentative de connexion à votre compte de "
"réseau social."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Vous pouvez vous connecter à votre compte en utilisant l'un des comptes "
"tiers suivants :"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Aucun compte social n'est actuellement associé à ce compte."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Ajouter un compte tiers"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "Un compte tiers de %(provider)s a été associé à votre compte."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Compte tiers ajouté"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "Un compte tiers de %(provider)s a été dissocié de votre compte."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Compte tiers dissocié"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Connecter %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Vous êtes sur le point de connecter un nouveau compte tiers de %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Connexion via %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Vous êtes sur le point de vous connecter en utilisant un compte tiers de "
"%(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Continuer"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Connexion annulée"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Vous avez annulé la connexion à notre site depuis l'un de vos comptes de "
"réseau social. S'il s'agit d'une erreur, merci de vous <a "
"href=\"%(login_url)s\">reconnecter</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Le compte social a bien été connecté."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Le compte social a été déconnecté."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Vous êtes sur le point de vous connecter via votre compte %(provider_name)s\n"
"au site %(site_name)s. Merci de compléter le formulaire suivant pour "
"confirmer la connexion :"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Ou utilisez un service tiers"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Déconnecté de toutes les autres sessions."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Commencé à"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "Adresse IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Navigateur"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Vu pour la dernière fois à"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Actuel"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Déconnecter les autres sessions"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Sessions utilisateur"

#: usersessions/models.py:92
msgid "session key"
msgstr "Clé de session"

#~ msgid "Account Connection"
#~ msgstr "Connexion au compte"

#, fuzzy
#~ msgid "Use security key or device"
#~ msgstr "Utilisez votre clé de sécurité ou appareil"

#, fuzzy
#~ msgid "Add Security Key or Device"
#~ msgstr "Ajouter une clé de sécurité ou un appareil"

#~ msgid "Add key or device"
#~ msgstr "Ajouter une clé ou un appareil"

#, fuzzy
#~ msgid "Security Keys and Devices"
#~ msgstr "Clés de sécurité et appareils"

#, fuzzy
#~ msgid "You have not added any security keys/devices."
#~ msgstr "Vous n'avez pas ajouté de clés de sécurité ou d'appareils."

#, fuzzy
#~ msgid "Edit Security Key or Device"
#~ msgstr "Modifier la clé de sécurité ou l'appareil"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Le mot de passe doit contenir au minimum {0} caractères."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "\"Vous recevez cet e-mail parce que vous ou quelqu'un d'autre a demandé "
#~ "un mot de\n"
#~ "passe pour votre compte utilisateur. Cependant, nous n'avons aucun\n"
#~ "enregistrement d'un utilisateur avec l'e-mail %(email)s dans notre base "
#~ "de\n"
#~ "données.\n"
#~ "\n"
#~ "Vous pouvez ignorer en toute sécurité cet e-mail si vous n'avez pas "
#~ "demandé de\n"
#~ "réinitialisation de mot de passe.\n"
#~ "\n"
#~ "Si c'était vous, vous pouvez vous inscrire pour un compte en utilisant le "
#~ "lien\n"
#~ "ci-dessous."

#~ msgid "The following email address is associated with your account:"
#~ msgstr "Les adresses email suivantes sont associées à votre compte :"

#~ msgid "Change Email Address"
#~ msgstr "Changer l'adresse e-mail"

#~ msgid ""
#~ "To safeguard the security of your account, please enter your password:"
#~ msgstr ""
#~ "Pour garantir la sécurité de votre compte, veuillez entrer votre mot de "
#~ "passe:"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Merci d'ouvrir une session avec l'un de vos comptes sociaux. Vous pouvez "
#~ "aussi %(link)souvrir un compte%(end_link)s %(site_name)s puis vous "
#~ "connecter ci-dessous :"

#~ msgid "or"
#~ msgstr "ou"

#~ msgid "change password"
#~ msgstr "modifier le mot de passe"

#~ msgid "OpenID Sign In"
#~ msgstr "Connexion OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "L'adresse e-mail est déjà associée à un autre compte."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Nous vous avons envoyé un e-mail. Merci de nous contacter si vous ne le "
#~ "recevez pas d'ici quelques minutes."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "L'identifiant ou le mot de passe sont incorrects."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Un pseudonyme ne peut contenir que des lettres, des chiffres, ainsi que "
#~ "@/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Ce pseudonyme est déjà utilisé, merci d'en choisir un autre."

#~ msgid "Shopify Sign In"
#~ msgstr "Connexion Shopify"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Vous avez confirmé que l'adresse e-mail de l'utilsateur %(user_display)s "
#~ "est <a href=\"mailto:%(email)s\">%(email)s</a>."

#~ msgid "Thanks for using our site!"
#~ msgstr "Merci d'utiliser notre site !"
