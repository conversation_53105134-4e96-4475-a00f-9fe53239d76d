# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-04-20 21:51+0200\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : "
"2);\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Šis konts šobrīd ir neaktīvs."

#: account/adapter.py:57
#, fuzzy
#| msgid "You cannot remove your primary email address (%(email)s)."
msgid "You cannot remove your primary email address."
msgstr "Jūs nevarat noņemt savu primāro e-pasta adresi (%(email)s)."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Šī e-pasta adrese jau ir piesaistīta šim kontam."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Nepareizs e-pasts un/vai parole."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "Nepareizs lietotāja vārds un/vai parole."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Lietotājs ar šādu e-pasta adresi jau ir reģistrēts."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Lūdzu ievadiet jūsu šobrīdējo paroli."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr ""

#: account/adapter.py:71
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Šobrīdējā parole"

#: account/adapter.py:72
#, fuzzy
#| msgid "Bad Token"
msgid "Invalid or expired key."
msgstr "Nederīgs marķieris"

#: account/adapter.py:73
#, fuzzy
#| msgid "Bad Token"
msgid "Invalid login."
msgstr "Nederīgs marķieris"

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Paroles atjaunošanas marķieris bija nederīgs."

#: account/adapter.py:75
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Jūsu kontam nav apstiprinātas e-pasta adreses."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Lietotājs ar šādu e-pasta adresi jau ir reģistrēts."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Pārāk daudz neveiksmīgi pieslēgšanās mēģinājumi. Mēģiniet vēlreiz vēlāk."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "E-pasta adrese nav piesaistīta nevienam lietotāja kontam"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "E-pasta adrese nav piesaistīta nevienam lietotāja kontam"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Jūsu primārajai e-pasta adresei jābūt apstiprinātai."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Lietotājvārds nevar tikt izmantots. Lūdzu izvēlietis citu lietotājvārdu."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Nepareizs lietotāja vārds un/vai parole."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
#, fuzzy
#| msgid "Forgot Password?"
msgid "Use your password"
msgstr "Aizmirsāt paroli?"

#: account/adapter.py:787
#, fuzzy
#| msgid "token secret"
msgid "Use authenticator app or code"
msgstr "token secret"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
#, fuzzy
#| msgid "secret key"
msgid "Use a security key"
msgstr "secret key"

#: account/admin.py:23
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Mark selected email addresses as verified"
msgstr "Jūsu primārajai e-pasta adresei jābūt apstiprinātai."

#: account/apps.py:11
msgid "Accounts"
msgstr "Konti"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Katru reizi jums ir jāievada tā pati parole."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Parole"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Atcerēties mani"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "E-pasta adrese"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-pasts"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Lietotājvārds"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Ieiet"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Lietotājvārds vai e-pasts"

#: account/forms.py:156
msgid "Username or email"
msgstr "Lietotājvārds vai e-pasts"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Lietotājvārds vai e-pasts"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "E-pasts (izvēles)"

#: account/forms.py:183
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "Aizmirsāt paroli?"

#: account/forms.py:334
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "E-pasts (izvēles)"

#: account/forms.py:339
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "e-pasta apstiprinājums"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-pasts (izvēles)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "E-pasts (izvēles)"

#: account/forms.py:435
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "Katru reizi jums ir jāievada tā pati parole."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Parole (vēlreiz)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Šobrīdējā parole"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Jaunā parole"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Jaunā parole (vēlreiz)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr ""

#: account/models.py:26
msgid "user"
msgstr "lietotājs"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "e-pasta adrese"

#: account/models.py:34
msgid "verified"
msgstr "apstiprināts"

#: account/models.py:35
msgid "primary"
msgstr "primārā"

#: account/models.py:41
msgid "email addresses"
msgstr "e-pasta adreses"

#: account/models.py:151
msgid "created"
msgstr "izveidots"

#: account/models.py:152
msgid "sent"
msgstr "nosūtīts"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "atslēga"

#: account/models.py:158
msgid "email confirmation"
msgstr "e-pasta apstiprinājums"

#: account/models.py:159
msgid "email confirmations"
msgstr "e-pasta apstiprinājumi"

#: headless/apps.py:7
msgid "Headless"
msgstr ""

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr ""

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:141
#, fuzzy
#| msgid "secret key"
msgid "Master key"
msgstr "secret key"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr ""

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr ""

#: mfa/apps.py:9
msgid "MFA"
msgstr ""

#: mfa/models.py:24
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr ""

#: mfa/models.py:26
msgid "WebAuthn"
msgstr ""

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr ""

#: mfa/webauthn/forms.py:59
#, fuzzy
#| msgid "Password"
msgid "Passwordless"
msgstr "Parole"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""

#: socialaccount/adapter.py:35
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Jau eksistē konts ar šo e-pasta adresi. Lūdzu sākumā ieejiet tajā kontā, tad "
"pievienojiet %s kontu."

#: socialaccount/adapter.py:39
#, fuzzy
#| msgid "Bad Token"
msgid "Invalid token."
msgstr "Nederīgs marķieris"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Jūsu kontam nav uzstādīta parole."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Jūsu kontam nav apstiprinātas e-pasta adreses."

#: socialaccount/adapter.py:43
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Jūs varat ieiet jūsu kontā izmantojot jebkuru no sekojošiem trešo pušu "
"kontiem:"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "Sociālais konts jau ir piesaistīts citam kontam."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Sociālie konti"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "sniedzējs"

#: socialaccount/models.py:52
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "sniedzējs"

#: socialaccount/models.py:56
msgid "name"
msgstr "vārds"

#: socialaccount/models.py:58
msgid "client id"
msgstr "klienta id"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "App ID, vai consumer key"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "secret key"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret, vai consumer secret"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Key"

#: socialaccount/models.py:81
msgid "social application"
msgstr "sociālā aplikācija"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "sociālās aplikācijas"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "pēdējā pieslēgšanās"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "reģistrācijas datums"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "papildus informācija"

#: socialaccount/models.py:125
msgid "social account"
msgstr "sociālais konts"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "sociālie konti"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) vai piekļūt marķierim (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token secret"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) vai atjaunot marķieri (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "beidzas"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "sociālās aplikācijas marķieris"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "sociālās aplikācijas marķieri"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
#, fuzzy
#| msgctxt "field label"
#| msgid "Login"
msgid "Login"
msgstr "Ieiet"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Nederīga atbilde iegūstot pieprasījuma marķieri no \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Nederīga atbilde iegūstot pieejas marķieri no \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Netika saglabāts pieprasījuma marķieris priekš \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Netika saglabāts pieejas marķieris priekš  \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nav pieejas privātam resursam \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Nederīga atbilde iegūstot pieprasījuma marķieri no \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Neaktīvs konts"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Šis konts ir neaktīvs."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Apstiprināt"

#: templates/account/base_confirm_code.html:43
msgid "Request new code"
msgstr ""

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Apstipriniet e-pasta adresi"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr ""

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr ""

#: templates/account/confirm_email_verification_code.html:5
#, fuzzy
#| msgid "email confirmation"
msgid "Email Verification"
msgstr "e-pasta apstiprinājums"

#: templates/account/confirm_email_verification_code.html:8
#, fuzzy
#| msgid "token secret"
msgid "Enter Email Verification Code"
msgstr "token secret"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "e-pasta adrese"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Ieiet"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr ""

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Atjaunot paroli"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Atjaunot paroli"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Pārsūtīt apstiprināšanu"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "token secret"
msgid "Enter Phone Verification Code"
msgstr "token secret"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-pasta adreses"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Sekojošas e-pasta adreses ir piesaistītas jūsu kontam:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Apstiprināta"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Neapstiprināta"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Primārā"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Iestatīt kā primāro"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Pārsūtīt apstiprināšanu"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Noņemt"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Pievienot e-pasta adresi"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Pievienot e-pastu"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Vai jūs tiešām vēlaties noņemt izvēlēto e-pasta adresi?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, fuzzy, python-format
#| msgid ""
#| "Thank you from %(site_name)s!\n"
#| "%(site_domain)s"
msgid "Hello from %(site_name)s!"
msgstr ""
"Visu labu vēlot, %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Paldies, ka izmantojat %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Email address"
msgid "Email Changed"
msgstr "E-pasta adrese"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "Your email has been confirmed."
msgstr "Jūs esat apstiprinājis %(email)s."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "e-pasta apstiprinājums"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because user %(user_display)s at "
#| "%(site_domain)s has given yours as an e-mail address to connect their "
#| "account.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Sveiciens no %(site_name)s!\n"
"\n"
"Jūs saņemat šo e-pasta vēstuli tāpēc, ka lietotājs %(user_display)s lapā "
"%(site_domain)s ir iesniedzis šo e-pasta adresi, lai tā tiktu sasaistīta ar "
"viņa kontu.\n"
"\n"
"Lai apstiprinātu, ka viss ir parezi, ejiet uz %(activate_url)s\n"

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Lūdzu apstipriniet savu e-pasta adresi"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "Noņemt"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""

#: templates/account/email/login_code_subject.txt:3
#, fuzzy
#| msgid "Sign In"
msgid "Sign-In Code"
msgstr "Ieiet"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "Jūsu parole ir nomainīta."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "Parole (vēlreiz)"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Atjaunot paroli"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Sveiciens no %(site_name)s!\n"
"\n"
"Jūs saņemat šo e-pasta vēstuli tāpēc, ka jūs vai kāds cits ir pieprasījis "
"paroles atjaunošanu jūsu kontam lapā %(site_domain)s.\n"
"Jūs varat droši ignorēt šo e-pasta vēstuli, ja jūs nepieprasījat paroles "
"atjaunošanu. Spiedied uz linka zemāk, lai atjaunotu jūsu paroli."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Gadījumā ja jūs aizmirsāt, tad jūsu lietotājvārds ir %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Paroles atjaunošanas e-pasts"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been reset."
msgstr "Jūsu parole ir nomainīta."

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been set."
msgstr "Jūsu parole ir nomainīta."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "Atjaunot paroli"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""

#: templates/account/email/unknown_account_subject.txt:3
#, fuzzy
#| msgid "Account"
msgid "Unknown Account"
msgstr "Konts"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "E-pasta adreses"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
#, fuzzy
#| msgid "Current Password"
msgid "Current email"
msgstr "Šobrīdējā parole"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr ""

#: templates/account/email_change.html:35
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification."
msgstr "Jūsu primārajai e-pasta adresei jābūt apstiprinātai."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr ""

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
#, fuzzy
#| msgid "Email"
msgid "Change to"
msgstr "E-pasts"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-pasts"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Apstipriniet e-pasta adresi"

#: templates/account/email_confirm.html:16
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Lūdzu apstipriniet ka <a href=\"mailto:%(email)s\">%(email)s</a> e-pasta "
"adrese pieder lietotājam %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Sociālais konts jau ir piesaistīts citam kontam."

#: templates/account/email_confirm.html:36
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a "
#| "href=\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Šī e-pasta apstiprināšanas saitei ir beidzies derīguma termiņš vai tas ir "
"nederīgs. Lūdzu <a href=\"%(email_url)s\">pieprasiet jaunu e-pasta "
"apstiprināšanas linku</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Ja jūs vēl neesat izveidojuši kontu, tad lūdzu "
"%(link)spiereģistrējaties%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr ""

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr ""

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Iziet"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Vai jūs tiešām vēlaties iziet?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Jūs nevarat noņemt savu primāro e-pasta adresi (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Apstiprinājuma e-pasts nosūtīts uz %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Jūs esat apstiprinājis %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Noņemta e-pasta adrese %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Veiksmīgi iegājuši kā %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Jūs esat izgājuši."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr ""

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Parole veiksmīgi nomainīta."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Parole veiksmīgi uzstādīta."

#: templates/account/messages/phone_verification_sent.txt:2
#, python-format
msgid "A verification code has been sent to %(phone)s."
msgstr ""

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primārā e-pasta adrese uzstādīta."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Mainīt paroli"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Aizmirsāt paroli?"

#: templates/account/password_reset.html:14
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Aizmirsāt savu paroli? Ievadiet zemāk savu e-pasta adresi, un mēs jums "
"nosūtīsim e-pasta vēstuli, lai atjaunotu to."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Atjaunot manu paroli"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Lūdzu sazinieties ar mums, ja jums ir kādas problēmas atjaunojot to."

#: templates/account/password_reset_done.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Mēs esam jums nosūtījuši e-pastu\n"
"apstiprināšanai. Lūdzu klikšķiniet uz saites šajā e-pastā. Lūdzu\n"
"sazinieties ar mums, ja dažu minūšu laikā nesaņemat vēstuli."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Nederīgs marķieris"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Paroles atjaunošanas links ir nederīgs, iespējams, tāpēc ka tas jau ir "
"izmantots. Lūdzu pieprasiet <a href=\"%(passwd_reset_url)s\">jaunu paroles "
"atjaunošanu</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Jūsu parole ir nomainīta."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Uzstādīt paroli"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Email"
msgid "Change Phone"
msgstr "E-pasts"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current Password"
msgid "Current phone"
msgstr "Šobrīdējā parole"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your phone number is still pending verification."
msgstr "Jūsu primārajai e-pasta adresei jābūt apstiprinātai."

#: templates/account/reauthenticate.html:6
#, fuzzy
#| msgid "Forgot Password?"
msgid "Enter your password:"
msgstr "Aizmirsāt paroli?"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr ""

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr ""

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr ""

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Reģistrēties"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Reģistrēties"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Jau ir konts? Tad lūdzu %(link)sspiedied šeit%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr ""

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "Reģistrēties"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr ""

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Reģistrācija slēgta"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Mēs atvainojamies, bet reģistrācija šobrīd ir slēgta."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Piezīme"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "jūs jau esat iegājuši kā %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Brīdinājums:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Jums pašlaik nav izveidota neviena e-pasta adrese. Jums patiešām vajadzētu "
"pievienot e-pasta adresi, lai jūs varētu saņemt paziņojumus, atiestatīt savu "
"paroli utt."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Apstipriniet savu e-pasta adresi"

#: templates/account/verification_sent.html:12
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Jums ir nosūtīts apstiprinājuma e-pasts. Sekojiet saitei, lai pabeigu "
"reģistrācijas procesu. Lūdzu sazinieties ar mums, ja dažu minūšu laikā "
"nesaņemat vēstuli."

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Šī lapas daļa mums nosaka pārbaudīt, ka\n"
"jūs esat tas, kas jūs apgalvojat esam. Šim mērķim, mēs pieprasām, lai jūs\n"
"apstipriniet, jūsu e-pasta adreses piederību. "

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Mēs esam jums nosūtījuši e-pastu\n"
"apstiprināšanai. Lūdzu klikšķiniet uz saites šajā e-pastā. Lūdzu\n"
"sazinieties ar mums, ja dažu minūšu laikā nesaņemat vēstuli."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Piezīme:</strong> jūs vēljoprojām varat <a "
"href=\"%(email_url)s\">nomainīt jūsu e-pasta adresi</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Konta savienojumi"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr ""

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/email/totp_activated_subject.txt:3
#, fuzzy
#| msgid "token secret"
msgid "Authenticator App Activated"
msgstr "token secret"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "token secret"
msgid "Authenticator App Deactivated"
msgstr "token secret"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr ""

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr ""

#: templates/mfa/email/webauthn_removed_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "A security key has been removed."
msgstr "Jūs esat apstiprinājis %(email)s."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr ""

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr ""

#: templates/mfa/index.html:62
msgid "Manage"
msgstr ""

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr ""

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:96
msgid "View"
msgstr ""

#: templates/mfa/index.html:102
msgid "Download"
msgstr ""

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr ""

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr ""

#: templates/mfa/reauthenticate.html:6
#, fuzzy
#| msgid "token secret"
msgid "Enter an authenticator code:"
msgstr "token secret"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "token secret"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Add Security Key"
msgstr "secret key"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
#, fuzzy
#| msgid "secret key"
msgid "Remove Security Key"
msgstr "secret key"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
#, fuzzy
#| msgid "Are you sure you want to sign out?"
msgid "Are you sure you want to remove this security key?"
msgstr "Vai jūs tiešām vēlaties iziet?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:37
#, fuzzy
#| msgid "secret key"
msgid "Security key"
msgstr "secret key"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:41
#, fuzzy
#| msgid "Unverified"
msgid "Unspecified"
msgstr "Neapstiprināta"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Edit Security Key"
msgstr "secret key"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr ""

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Current Password"
msgid "Create Passkey"
msgstr "Šobrīdējā parole"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "izveidots"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "Sociālā tīkla ieiešanas kļūda"

#: templates/socialaccount/authentication_error.html:12
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Notikusi kļūme, mēģinot ieiet ar jūsu sociālo kontu."

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Jūs varat ieiet jūsu kontā izmantojot jebkuru no sekojošiem trešo pušu "
"kontiem:"

#: templates/socialaccount/connections.html:46
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "Jūsu kontam šobrīd nav piesaistīts neviens sociālais konts."

#: templates/socialaccount/connections.html:50
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "Pievienot trešās puses kontu"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Connected"
msgstr "Pievienot trešās puses kontu"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Disconnected"
msgstr "Pievienot trešās puses kontu"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Ieiešana pārtraukta"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Jūs nolēmāt pārtraukt ieiešanu mūsu lapa izmantojot vienu no jūsu kontiem. "
"Ja šī ir kļūda, lūdzu dodaties uz <a href=\"%(login_url)s\">ieiet</a>."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "Sociālais konts ir piesaistīts."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "Sociālais konts ir atvienots."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Jūs izmantosiet savu %(provider_name)s kontu, lai ieietu\n"
"%(site_name)s. Kā pēdējo soli, lūdzu aizpildiet sekojošo formu:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr ""

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Addresses"
msgid "IP Address"
msgstr "E-pasta adreses"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
#, fuzzy
#| msgid "Current Password"
msgid "Current"
msgstr "Šobrīdējā parole"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:92
msgid "session key"
msgstr ""

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Konta savienojumi"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Parolei jābūt vismaz {0} simbolus garai."

#, fuzzy, python-format
#~| msgid ""
#~| "Hello from %(site_name)s!\n"
#~| "\n"
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account at %(site_domain)s.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Sveiciens no %(site_name)s!\n"
#~ "\n"
#~ "Jūs saņemat šo e-pasta vēstuli tāpēc, ka jūs vai kāds cits ir pieprasījis "
#~ "paroles atjaunošanu jūsu kontam lapā %(site_domain)s.\n"
#~ "Jūs varat droši ignorēt šo e-pasta vēstuli, ja jūs nepieprasījat paroles "
#~ "atjaunošanu. Spiedied uz linka zemāk, lai atjaunotu jūsu paroli."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Sekojošas e-pasta adreses ir piesaistītas jūsu kontam:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Apstipriniet e-pasta adresi"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Lūdzu ieejiet iekšā ar vienu\n"
#~ "no jūsu eksistējošiem trešās puses kontiem. Vai <a "
#~ "href=\"%(signup_url)s\">reģistrējieties</a>\n"
#~ "%(site_name)s kontam un ieejiet zemāk:"

#~ msgid "or"
#~ msgstr "vai"

#~ msgid "change password"
#~ msgstr "Nomainīt paroli"

#~ msgid "OpenID Sign In"
#~ msgstr "Ieiet ar OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Šī e-pasta adrese jau ir piesaistīta citam kontam."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Mēs jums nosūtījām e-pasta vēstuli. Lūdzu sazinieties ar mums, ja dažu "
#~ "minūšu laikā nesaņemat vēstuli."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Nepareiza pieteikšanās informācija un/vai parole."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Lietotājvārds var saturēt tikai burtus, ciparus un @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Šis lietotājvārds jau ir aizņemts. Lūdzu izvēlaties citu."

#~ msgid "Shopify Sign In"
#~ msgstr "Ieiet ar Shopify"
