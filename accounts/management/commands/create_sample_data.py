from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Branch, UserProfile
from inventory.models import Category, Subcategory, Brand, Product, Stock
from core.models import SiteConfiguration
from decimal import Decimal

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample data for YalaOffice'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample data...'))

        # Create site configuration
        site_config, created = SiteConfiguration.objects.get_or_create(
            id=1,
            defaults={
                'company_name': 'YalaOffice',
                'company_address': '123 Business Street, Casablanca, Morocco',
                'company_phone': '+*********** 456',
                'company_email': '<EMAIL>',
                'default_currency': 'Dh',
                'tax_rate': Decimal('20.00'),
                'minimum_order_amount': Decimal('50.00'),
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Site configuration created'))

        # Create branches
        branches_data = [
            {
                'name': 'Casablanca Main Branch',
                'address': '123 Hassan II Boulevard, Casablanca',
                'city': 'Casablanca',
                'phone': '+*********** 456',
                'email': '<EMAIL>',
                'latitude': Decimal('33.5731'),
                'longitude': Decimal('-7.5898'),
            },
            {
                'name': 'Rabat Branch',
                'address': '456 Mohammed V Avenue, Rabat',
                'city': 'Rabat',
                'phone': '+*********** 456',
                'email': '<EMAIL>',
                'latitude': Decimal('34.0209'),
                'longitude': Decimal('-6.8416'),
            },
            {
                'name': 'Marrakech Branch',
                'address': '789 Jemaa el-Fnaa Square, Marrakech',
                'city': 'Marrakech',
                'phone': '+212 524 123 456',
                'email': '<EMAIL>',
                'latitude': Decimal('31.6295'),
                'longitude': Decimal('-7.9811'),
            }
        ]

        for branch_data in branches_data:
            branch, created = Branch.objects.get_or_create(
                name=branch_data['name'],
                defaults=branch_data
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'✓ Branch created: {branch.name}'))

        # Create users
        users_data = [
            {
                'username': 'manager1',
                'email': '<EMAIL>',
                'first_name': 'Ahmed',
                'last_name': 'Benali',
                'role': 'manager',
                'phone': '+212 661 123 456',
            },
            {
                'username': 'delivery1',
                'email': '<EMAIL>',
                'first_name': 'Omar',
                'last_name': 'Alami',
                'role': 'delivery',
                'phone': '+212 662 123 456',
            },
            {
                'username': 'client1',
                'email': '<EMAIL>',
                'first_name': 'Fatima',
                'last_name': 'Zahra',
                'role': 'client',
                'phone': '+212 663 123 456',
            },
            {
                'username': 'reseller1',
                'email': '<EMAIL>',
                'first_name': 'Hassan',
                'last_name': 'Idrissi',
                'role': 'reseller',
                'phone': '+212 664 123 456',
            }
        ]

        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'role': user_data['role'],
                    'phone': user_data['phone'],
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                
                # Create profile
                profile_data = {
                    'user': user,
                    'city': 'Casablanca',
                    'preferred_branch': Branch.objects.first(),
                }
                
                if user.role == 'reseller':
                    profile_data.update({
                        'company_name': 'Idrissi Office Supplies',
                        'tax_id': 'TAX123456789',
                    })
                
                UserProfile.objects.get_or_create(user=user, defaults=profile_data)
                self.stdout.write(self.style.SUCCESS(f'✓ User created: {user.username} ({user.role})'))

        # Assign manager to first branch
        manager = User.objects.filter(role='manager').first()
        if manager:
            branch = Branch.objects.first()
            branch.manager = manager
            branch.save()
            self.stdout.write(self.style.SUCCESS(f'✓ Manager assigned to {branch.name}'))

        # Create brands
        brands_data = [
            'BIC', 'Pilot', 'Stabilo', 'Faber-Castell', 'Staedtler',
            'Papermate', 'Sharpie', 'Uni-ball', 'Pentel', 'Zebra'
        ]

        for brand_name in brands_data:
            brand, created = Brand.objects.get_or_create(
                name=brand_name,
                defaults={'description': f'{brand_name} office supplies'}
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'✓ Brand created: {brand.name}'))

        # Create categories and subcategories
        categories_data = [
            {
                'name': 'Writing Instruments',
                'subcategories': ['Pens', 'Pencils', 'Markers', 'Highlighters']
            },
            {
                'name': 'Paper & Notebooks',
                'subcategories': ['Notebooks', 'Copy Paper', 'Sticky Notes', 'Envelopes']
            },
            {
                'name': 'School & Office Supplies',
                'subcategories': ['Staplers', 'Scissors', 'Rulers', 'Calculators']
            },
            {
                'name': 'Art & Craft Supplies',
                'subcategories': ['Colored Pencils', 'Paint', 'Brushes', 'Canvas']
            },
            {
                'name': 'Filing & Organization',
                'subcategories': ['Folders', 'Binders', 'File Boxes', 'Labels']
            }
        ]

        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': f'{cat_data["name"]} for office and school use'}
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'✓ Category created: {category.name}'))

            for subcat_name in cat_data['subcategories']:
                subcategory, created = Subcategory.objects.get_or_create(
                    category=category,
                    name=subcat_name,
                    defaults={'description': f'{subcat_name} subcategory'}
                )
                if created:
                    self.stdout.write(self.style.SUCCESS(f'  ✓ Subcategory created: {subcategory.name}'))

        # Create sample products
        products_data = [
            {
                'title': 'BIC Ballpoint Pen Blue',
                'description': 'Classic blue ballpoint pen, smooth writing experience',
                'category': 'Writing Instruments',
                'subcategory': 'Pens',
                'brand': 'BIC',
                'normal_price': Decimal('2.50'),
                'reseller_price': Decimal('1.80'),
                'sku': 'BIC-PEN-BLUE-001',
            },
            {
                'title': 'A4 Copy Paper 500 Sheets',
                'description': 'High quality white copy paper, 80gsm',
                'category': 'Paper & Notebooks',
                'subcategory': 'Copy Paper',
                'brand': 'Papermate',
                'normal_price': Decimal('25.00'),
                'reseller_price': Decimal('18.00'),
                'sku': 'PAPER-A4-500-001',
            },
            {
                'title': 'Stapler Heavy Duty',
                'description': 'Professional heavy duty stapler for office use',
                'category': 'School & Office Supplies',
                'subcategory': 'Staplers',
                'brand': 'Staedtler',
                'normal_price': Decimal('45.00'),
                'reseller_price': Decimal('32.00'),
                'sku': 'STAPLER-HD-001',
            },
            {
                'title': 'Colored Pencils Set 24',
                'description': 'Professional colored pencils set with 24 vibrant colors',
                'category': 'Art & Craft Supplies',
                'subcategory': 'Colored Pencils',
                'brand': 'Faber-Castell',
                'normal_price': Decimal('35.00'),
                'reseller_price': Decimal('25.00'),
                'sku': 'PENCIL-COLOR-24-001',
            },
            {
                'title': 'Ring Binder A4',
                'description': 'Durable A4 ring binder for document organization',
                'category': 'Filing & Organization',
                'subcategory': 'Binders',
                'brand': 'Pilot',
                'normal_price': Decimal('15.00'),
                'reseller_price': Decimal('11.00'),
                'sku': 'BINDER-A4-001',
            }
        ]

        for prod_data in products_data:
            category = Category.objects.get(name=prod_data['category'])
            subcategory = Subcategory.objects.get(
                category=category, 
                name=prod_data['subcategory']
            )
            brand = Brand.objects.get(name=prod_data['brand'])

            product, created = Product.objects.get_or_create(
                sku=prod_data['sku'],
                defaults={
                    'title': prod_data['title'],
                    'description': prod_data['description'],
                    'category': category,
                    'subcategory': subcategory,
                    'brand': brand,
                    'normal_price': prod_data['normal_price'],
                    'reseller_price': prod_data['reseller_price'],
                    'is_active': True,
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'✓ Product created: {product.title}'))

                # Create stock for each branch
                for branch in Branch.objects.all():
                    Stock.objects.get_or_create(
                        product=product,
                        branch=branch,
                        defaults={
                            'quantity': 100,
                            'minimum_stock': 10,
                            'maximum_stock': 500,
                        }
                    )

        self.stdout.write(self.style.SUCCESS('\n🎉 Sample data creation completed successfully!'))
        self.stdout.write(self.style.SUCCESS('\nYou can now:'))
        self.stdout.write(self.style.SUCCESS('1. Login to admin with: <EMAIL> / admin123'))
        self.stdout.write(self.style.SUCCESS('2. Test different user roles:'))
        self.stdout.write(self.style.SUCCESS('   - Manager: <EMAIL> / password123'))
        self.stdout.write(self.style.SUCCESS('   - Delivery: <EMAIL> / password123'))
        self.stdout.write(self.style.SUCCESS('   - Client: <EMAIL> / password123'))
        self.stdout.write(self.style.SUCCESS('   - Reseller: <EMAIL> / password123'))
        self.stdout.write(self.style.SUCCESS('3. Access dashboards at: /accounts/dashboard/'))
