from django.db import models
from django.contrib.auth import get_user_model
from orders.models import Order

User = get_user_model()


class DeliveryAssignment(models.Model):
    """Assignment of orders to delivery personnel"""

    STATUS_CHOICES = (
        ('assigned', 'Assigned'),
        ('picked', 'Picked'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed Delivery'),
        ('returned', 'Returned'),
    )

    order = models.OneToOneField(Order, on_delete=models.CASCADE, related_name='delivery_assignment')
    delivery_person = models.ForeignKey(User, on_delete=models.CASCADE, related_name='delivery_assignments')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='assigned')

    # Assignment details
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assigned_deliveries')
    assigned_at = models.DateTimeField(auto_now_add=True)

    # Status timestamps
    picked_at = models.DateTimeField(null=True, blank=True)
    out_for_delivery_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)

    # Delivery details
    delivery_notes = models.TextField(blank=True)
    customer_signature = models.ImageField(upload_to='signatures/', null=True, blank=True)
    delivery_photo = models.ImageField(upload_to='delivery_photos/', null=True, blank=True)

    # Failed delivery
    failure_reason = models.TextField(blank=True)
    retry_count = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Delivery {self.order.order_number} - {self.delivery_person.get_full_name()}"

    class Meta:
        ordering = ['-created_at']


class DeliveryStatusUpdate(models.Model):
    """Track delivery status updates"""

    delivery_assignment = models.ForeignKey(DeliveryAssignment, on_delete=models.CASCADE, related_name='status_updates')
    status = models.CharField(max_length=20, choices=DeliveryAssignment.STATUS_CHOICES)
    notes = models.TextField(blank=True)
    location_latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    location_longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.delivery_assignment.order.order_number} - {self.status}"

    class Meta:
        ordering = ['-created_at']


class DeliveryRoute(models.Model):
    """Delivery routes for optimization"""

    delivery_person = models.ForeignKey(User, on_delete=models.CASCADE, related_name='delivery_routes')
    name = models.CharField(max_length=100)
    date = models.DateField()

    # Route details
    start_location_latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    start_location_longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    estimated_duration = models.DurationField(null=True, blank=True)
    actual_duration = models.DurationField(null=True, blank=True)

    # Status
    is_completed = models.BooleanField(default=False)
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Route {self.name} - {self.delivery_person.get_full_name()}"

    @property
    def total_deliveries(self):
        return self.deliveries.count()

    @property
    def completed_deliveries(self):
        return self.deliveries.filter(status='delivered').count()


class RouteDelivery(models.Model):
    """Deliveries in a route"""

    route = models.ForeignKey(DeliveryRoute, on_delete=models.CASCADE, related_name='deliveries')
    delivery_assignment = models.ForeignKey(DeliveryAssignment, on_delete=models.CASCADE)
    sequence_order = models.PositiveIntegerField()

    # Estimated vs actual
    estimated_arrival = models.DateTimeField(null=True, blank=True)
    actual_arrival = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Route {self.route.name} - Delivery {self.delivery_assignment.order.order_number}"

    class Meta:
        ordering = ['sequence_order']
        unique_together = ['route', 'delivery_assignment']
