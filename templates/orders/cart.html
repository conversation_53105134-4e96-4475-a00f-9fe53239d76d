{% extends 'base.html' %}

{% block title %}Shopping Cart - YalaOffice{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-shopping-cart"></i> Shopping Cart</h1>
            <a href="{% url 'inventory:product_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Continue Shopping
            </a>
        </div>
    </div>
</div>

{% if cart_items %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Cart Items ({{ cart.total_items }})</h5>
            </div>
            <div class="card-body">
                {% for item in cart_items %}
                <div class="row align-items-center border-bottom py-3" id="cart-item-{{ item.id }}">
                    <div class="col-md-2">
                        {% if item.product.main_image %}
                        <img src="{{ item.product.main_image.url }}" alt="{{ item.product.title }}" class="img-fluid rounded">
                        {% else %}
                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 80px;">
                            <i class="fas fa-image text-muted"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <h6 class="mb-1">{{ item.product.title }}</h6>
                        <small class="text-muted">SKU: {{ item.product.sku }}</small><br>
                        {% if item.product.brand %}
                        <small class="text-muted">Brand: {{ item.product.brand.name }}</small>
                        {% endif %}
                    </div>
                    <div class="col-md-2">
                        <div class="input-group input-group-sm">
                            <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity({{ item.id }}, {{ item.quantity }} - 1)">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" class="form-control text-center" value="{{ item.quantity }}" min="1" max="999" 
                                   id="quantity-{{ item.id }}" onchange="updateQuantity({{ item.id }}, this.value)">
                            <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity({{ item.id }}, {{ item.quantity }} + 1)">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="fw-bold">{{ item.get_unit_price }} Dh</div>
                        <small class="text-muted">per item</small>
                    </div>
                    <div class="col-md-1 text-center">
                        <div class="fw-bold text-primary" id="item-total-{{ item.id }}">{{ item.get_total_price }} Dh</div>
                    </div>
                    <div class="col-md-1 text-center">
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart({{ item.id }})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calculator"></i> Order Summary</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Subtotal ({{ cart.total_items }} items):</span>
                    <span class="fw-bold" id="cart-subtotal">{{ cart.total_amount }} Dh</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Shipping:</span>
                    <span class="text-success">Free</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between mb-3">
                    <span class="fw-bold">Total:</span>
                    <span class="fw-bold text-primary fs-5" id="cart-total">{{ cart.total_amount }} Dh</span>
                </div>
                
                <!-- Promo Code -->
                <div class="mb-3">
                    <label for="promo-code" class="form-label">Promo Code</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="promo-code" placeholder="Enter code">
                        <button class="btn btn-outline-secondary" type="button" onclick="applyPromoCode()">
                            Apply
                        </button>
                    </div>
                    <div id="promo-message" class="mt-2"></div>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="{% url 'orders:checkout' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-credit-card"></i> Proceed to Checkout
                    </a>
                    <a href="{% url 'inventory:product_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-shopping-bag"></i> Continue Shopping
                    </a>
                </div>
            </div>
        </div>
        
        <!-- User Pricing Info -->
        {% if user.is_reseller %}
        <div class="card mt-3">
            <div class="card-body text-center">
                <i class="fas fa-star text-warning"></i>
                <h6 class="mt-2">Reseller Pricing Applied</h6>
                <p class="text-muted small">You're getting wholesale prices on all items!</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% else %}
<!-- Empty Cart -->
<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
            <h3>Your cart is empty</h3>
            <p class="text-muted mb-4">Looks like you haven't added any items to your cart yet.</p>
            <a href="{% url 'inventory:product_list' %}" class="btn btn-primary">
                <i class="fas fa-shopping-bag"></i> Start Shopping
            </a>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function updateQuantity(itemId, newQuantity) {
    if (newQuantity < 1) {
        if (confirm('Remove this item from cart?')) {
            removeFromCart(itemId);
        }
        return;
    }
    
    fetch(`/orders/cart/update/${itemId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            quantity: newQuantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById(`quantity-${itemId}`).value = newQuantity;
            document.getElementById(`item-total-${itemId}`).textContent = data.item_total + ' Dh';
            document.getElementById('cart-subtotal').textContent = data.cart_total_amount + ' Dh';
            document.getElementById('cart-total').textContent = data.cart_total_amount + ' Dh';
            
            // Update cart badge in navigation if exists
            updateCartBadge(data.cart_total_items);
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the cart.');
    });
}

function removeFromCart(itemId) {
    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }
    
    fetch(`/orders/cart/remove/${itemId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById(`cart-item-${itemId}`).remove();
            document.getElementById('cart-subtotal').textContent = data.cart_total_amount + ' Dh';
            document.getElementById('cart-total').textContent = data.cart_total_amount + ' Dh';
            
            // Update cart badge in navigation if exists
            updateCartBadge(data.cart_total_items);
            
            // Reload page if cart is empty
            if (data.cart_total_items === 0) {
                location.reload();
            }
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while removing the item.');
    });
}

function applyPromoCode() {
    const code = document.getElementById('promo-code').value.trim();
    const cartTotal = parseFloat(document.getElementById('cart-total').textContent.replace(' Dh', ''));
    
    if (!code) {
        showPromoMessage('Please enter a promo code', 'danger');
        return;
    }
    
    fetch('/orders/api/apply-promo/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            code: code,
            cart_total: cartTotal
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showPromoMessage(data.message, 'success');
            document.getElementById('cart-total').textContent = data.new_total + ' Dh';
        } else {
            showPromoMessage(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showPromoMessage('An error occurred while applying the promo code', 'danger');
    });
}

function showPromoMessage(message, type) {
    const messageDiv = document.getElementById('promo-message');
    messageDiv.innerHTML = `<div class="alert alert-${type} alert-sm">${message}</div>`;
    setTimeout(() => {
        messageDiv.innerHTML = '';
    }, 5000);
}

function updateCartBadge(count) {
    const badge = document.querySelector('.cart-badge');
    if (badge) {
        badge.textContent = count;
        if (count === 0) {
            badge.style.display = 'none';
        }
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
