<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YalaOffice - Stock and Order Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-boxes"></i> YalaOffice
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">Admin</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/accounts/login/">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/accounts/signup/">Sign Up</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">Welcome to YalaOffice</h1>
            <p class="lead mb-4">Complete Stock and Order Management System for Office and School Supplies</p>
            <p class="mb-4">Manage your inventory, process orders, track deliveries, and grow your business with our comprehensive solution.</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h5>Multi-User Roles</h5>
                                <p class="small">Admin, Manager, Delivery, Client, Reseller</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="fas fa-store fa-2x mb-2"></i>
                                <h5>Multi-Branch Support</h5>
                                <p class="small">Manage multiple locations</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                                <h5>PWA Ready</h5>
                                <p class="small">Install on mobile devices</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col">
                    <h2 class="fw-bold">System Features</h2>
                    <p class="text-muted">Everything you need to manage your office supplies business</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <i class="fas fa-boxes feature-icon"></i>
                        <h5>Inventory Management</h5>
                        <p class="text-muted">Track stock levels, manage categories, and get low-stock alerts across multiple branches.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <i class="fas fa-shopping-cart feature-icon"></i>
                        <h5>Order Processing</h5>
                        <p class="text-muted">Complete order management with cart, checkout, payment tracking, and invoice generation.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <i class="fas fa-truck feature-icon"></i>
                        <h5>Delivery Management</h5>
                        <p class="text-muted">Assign deliveries, track status, collect signatures, and optimize delivery routes.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <i class="fas fa-chart-bar feature-icon"></i>
                        <h5>Analytics & Reports</h5>
                        <p class="text-muted">Comprehensive reporting with sales analytics, inventory insights, and performance metrics.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <i class="fas fa-bell feature-icon"></i>
                        <h5>Notifications</h5>
                        <p class="text-muted">Email and in-app notifications for orders, stock alerts, and delivery updates.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <i class="fas fa-tags feature-icon"></i>
                        <h5>Pricing & Discounts</h5>
                        <p class="text-muted">Different pricing for clients and resellers, promo codes, and discount management.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col">
                    <h2 class="fw-bold">Product Categories</h2>
                    <p class="text-muted">Wide range of office and school supplies</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-pen fa-2x text-primary mb-2"></i>
                        <h6>Writing Instruments</h6>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                        <h6>Paper & Notebooks</h6>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-school fa-2x text-primary mb-2"></i>
                        <h6>School Supplies</h6>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-palette fa-2x text-primary mb-2"></i>
                        <h6>Art & Craft</h6>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-folder fa-2x text-primary mb-2"></i>
                        <h6>Filing & Organization</h6>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-gift fa-2x text-primary mb-2"></i>
                        <h6>Greeting Cards</h6>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-desktop fa-2x text-primary mb-2"></i>
                        <h6>Office Accessories</h6>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="text-center">
                        <i class="fas fa-leaf fa-2x text-primary mb-2"></i>
                        <h6>Eco-Friendly</h6>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-boxes"></i> YalaOffice</h5>
                    <p class="text-muted">Complete Stock and Order Management System for Office and School Supplies</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">Built with Django & Bootstrap</p>
                    <p class="text-muted">Currency: Moroccan Dirham (Dh)</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
