{% load crispy_forms_field %}

{% if field.is_hidden %}
    {{ field }}
{% else %}
    {% if field|is_checkbox %}
        <div class="form-group{% if 'form-horizontal' in form_class %} row{% endif %}">
        {% if label_class %}
            <div class="{% for offset in bootstrap_checkbox_offsets %}{{ offset }} {% endfor %}{{ field_class }}">
        {% endif %}
    {% endif %}
    <{% if tag %}{{ tag }}{% else %}div{% endif %} id="div_{{ field.auto_id }}" class="{% if not field|is_checkbox %}form-group{% if 'form-horizontal' in form_class %} row{% endif %}{% else %}{%if use_custom_control%}{% if tag != 'td' %}custom-control {%endif%} custom-checkbox{% else %}form-check{% endif %}{% endif %}{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">
        {% if field.label and not field|is_checkbox and form_show_labels %}
        {# not field|is_radioselect in row below can be removed once Django 3.2 is no longer supported #}
        <label {% if field.id_for_label and not field|is_radioselect %}for="{{ field.id_for_label }}" {% endif %}class="{% if 'form-horizontal' in form_class %}col-form-label {% endif %}{{ label_class }}{% if field.field.required %} requiredField{% endif %}">
                {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
            </label>
        {% endif %}

        {% if field|is_checkboxselectmultiple %}
            {% include 'bootstrap4/layout/checkboxselectmultiple.html' %}
        {% endif %}

        {% if field|is_radioselect %}
            {% include 'bootstrap4/layout/radioselect.html' %}
        {% endif %}

        {% if not field|is_checkboxselectmultiple and not field|is_radioselect %}
            {% if field|is_checkbox %}
                {% if use_custom_control %}
                    {% if tag == 'td' %}
                        <div class="custom-control custom-checkbox">
                    {% endif %}

                    {% if field.errors %}
                        {% crispy_field field 'class' 'custom-control-input is-invalid' %}
                    {% else %}
                        {% crispy_field field 'class' 'custom-control-input' %}
                    {% endif %}
                {% else %}
                    {% if field.errors %}
                        {% crispy_field field 'class' 'form-check-input is-invalid' %}
                    {% else %}
                        {% crispy_field field 'class' 'form-check-input' %}
                    {% endif %}
                {% endif %}
                <label for="{{ field.id_for_label }}" class="{%if use_custom_control%}custom-control-label{% else %}form-check-label{% endif %}{% if field.field.required %} requiredField{% endif %}">
                    {% if form_show_labels %}
                        {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
                    {% else %}
                        &nbsp;
                    {% endif %}
                </label>
                {% include 'bootstrap4/layout/help_text_and_errors.html' %}
                {% if use_custom_control and tag == 'td' %}
                    </div>
                {% endif %}
            {% elif field|is_file and use_custom_control  %}
                {% include 'bootstrap4/layout/field_file.html' %}
            {% else %}
                <div{% if field_class %} class="{{ field_class }}"{% endif %}>
                    {% if field|is_select and use_custom_control %}
                        {% if field.errors %}
                            {% crispy_field field 'class' 'custom-select is-invalid' %}
                        {% else %}
                            {% crispy_field field 'class' 'custom-select' %}
                        {% endif %}
                    {% elif field|is_file %}
                        {% if field.errors %}
                            {% crispy_field field 'class' 'form-control-file is-invalid' %}
                        {% else %}
                            {% crispy_field field 'class' 'form-control-file' %}
                        {% endif %}
                    {% else %}
                        {% if field.errors %}
                            {% crispy_field field 'class' 'form-control is-invalid' %}
                        {% else %}
                            {% crispy_field field 'class' 'form-control' %}
                        {% endif %}
                    {% endif %}
                    {% include 'bootstrap4/layout/help_text_and_errors.html' %}
                </div>
            {% endif %}
        {% endif %}
    </{% if tag %}{{ tag }}{% else %}div{% endif %}>
    {% if field|is_checkbox %}
        {% if label_class %}
            </div>
        {% endif %}
        </div>
    {% endif %}
{% endif %}
