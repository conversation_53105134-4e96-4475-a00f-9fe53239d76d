from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from .models import Product, Category, Subcategory, Brand, Stock, ProductImage
from .forms import ProductForm, CategoryForm, BrandForm, StockForm, ProductImageForm
from accounts.models import Branch


@login_required
def product_list(request):
    """List all products with filtering and search"""
    products = Product.objects.select_related('category', 'subcategory', 'brand').prefetch_related('stock_items')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        products = products.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(sku__icontains=search_query) |
            Q(brand__name__icontains=search_query)
        )

    # Filter by category
    category_filter = request.GET.get('category', '')
    if category_filter:
        products = products.filter(category_id=category_filter)

    # Filter by brand
    brand_filter = request.GET.get('brand', '')
    if brand_filter:
        products = products.filter(brand_id=brand_filter)

    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter == 'active':
        products = products.filter(is_active=True)
    elif status_filter == 'inactive':
        products = products.filter(is_active=False)
    elif status_filter == 'featured':
        products = products.filter(is_featured=True)

    # Sorting
    sort_by = request.GET.get('sort', '-created_at')
    if sort_by in ['title', '-title', 'normal_price', '-normal_price', 'created_at', '-created_at']:
        products = products.order_by(sort_by)

    # Pagination
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    categories = Category.objects.filter(is_active=True)
    brands = Brand.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'brand_filter': brand_filter,
        'status_filter': status_filter,
        'sort_by': sort_by,
        'categories': categories,
        'brands': brands,
    }
    return render(request, 'inventory/product_list.html', context)


@login_required
def product_detail(request, product_id):
    """Product detail view"""
    product = get_object_or_404(Product, id=product_id)

    # Get stock information for all branches
    stock_items = Stock.objects.filter(product=product).select_related('branch')

    # Get additional images
    additional_images = ProductImage.objects.filter(product=product)

    # Calculate total stock across all branches
    total_stock = stock_items.aggregate(
        total_quantity=Sum('quantity'),
        total_available=Sum('quantity') - Sum('reserved_quantity')
    )

    context = {
        'product': product,
        'stock_items': stock_items,
        'additional_images': additional_images,
        'total_stock': total_stock,
    }
    return render(request, 'inventory/product_detail.html', context)


@login_required
def product_create(request):
    """Create new product (Admin/Manager only)"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('inventory:product_list')

    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES)
        if form.is_valid():
            product = form.save()

            # Create initial stock for all branches
            branches = Branch.objects.filter(is_active=True)
            for branch in branches:
                Stock.objects.create(
                    product=product,
                    branch=branch,
                    quantity=0,
                    minimum_stock=10,
                    maximum_stock=1000
                )

            messages.success(request, f'Product "{product.title}" created successfully!')
            return redirect('inventory:product_detail', product_id=product.id)
    else:
        form = ProductForm()

    context = {'form': form}
    return render(request, 'inventory/product_create.html', context)


@login_required
def product_edit(request, product_id):
    """Edit product (Admin/Manager only)"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('inventory:product_list')

    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            product = form.save()
            messages.success(request, f'Product "{product.title}" updated successfully!')
            return redirect('inventory:product_detail', product_id=product.id)
    else:
        form = ProductForm(instance=product)

    context = {
        'form': form,
        'product': product,
    }
    return render(request, 'inventory/product_edit.html', context)


@login_required
def product_delete(request, product_id):
    """Delete product (Admin only)"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('inventory:product_list')

    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        product_title = product.title
        product.delete()
        messages.success(request, f'Product "{product_title}" deleted successfully!')
        return redirect('inventory:product_list')

    context = {'product': product}
    return render(request, 'inventory/product_delete.html', context)


@login_required
def stock_management(request):
    """Stock management dashboard"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('inventory:product_list')

    # Get stock items with low stock alerts
    stock_items = Stock.objects.select_related('product', 'branch').all()

    # Filter by branch if specified
    branch_filter = request.GET.get('branch', '')
    if branch_filter:
        stock_items = stock_items.filter(branch_id=branch_filter)

    # Filter by stock status
    status_filter = request.GET.get('status', '')
    if status_filter == 'low':
        stock_items = [item for item in stock_items if item.is_low_stock]
    elif status_filter == 'out':
        stock_items = [item for item in stock_items if item.is_out_of_stock]

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        stock_items = stock_items.filter(
            Q(product__title__icontains=search_query) |
            Q(product__sku__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(stock_items, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    branches = Branch.objects.filter(is_active=True)

    # Calculate summary statistics
    total_products = Product.objects.filter(is_active=True).count()
    low_stock_count = len([item for item in Stock.objects.all() if item.is_low_stock])
    out_of_stock_count = len([item for item in Stock.objects.all() if item.is_out_of_stock])

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'branch_filter': branch_filter,
        'status_filter': status_filter,
        'branches': branches,
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
    }
    return render(request, 'inventory/stock_management.html', context)


@login_required
def stock_update(request, stock_id):
    """Update stock levels"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('inventory:stock_management')

    stock = get_object_or_404(Stock, id=stock_id)

    if request.method == 'POST':
        form = StockForm(request.POST, instance=stock)
        if form.is_valid():
            stock = form.save()
            if stock.quantity <= stock.minimum_stock:
                stock.last_restocked = timezone.now()
                stock.save()

            messages.success(request, f'Stock updated for {stock.product.title} at {stock.branch.name}')
            return redirect('inventory:stock_management')
    else:
        form = StockForm(instance=stock)

    context = {
        'form': form,
        'stock': stock,
    }
    return render(request, 'inventory/stock_update.html', context)


@login_required
def category_list(request):
    """List all categories"""
    categories = Category.objects.annotate(
        product_count=Count('products')
    ).order_by('name')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        categories = categories.filter(name__icontains=search_query)

    context = {
        'categories': categories,
        'search_query': search_query,
    }
    return render(request, 'inventory/category_list.html', context)


@login_required
def category_create(request):
    """Create new category (Admin/Manager only)"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('inventory:category_list')

    if request.method == 'POST':
        form = CategoryForm(request.POST, request.FILES)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'Category "{category.name}" created successfully!')
            return redirect('inventory:category_list')
    else:
        form = CategoryForm()

    context = {'form': form}
    return render(request, 'inventory/category_create.html', context)


@login_required
def brand_list(request):
    """List all brands"""
    brands = Brand.objects.annotate(
        product_count=Count('products')
    ).order_by('name')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        brands = brands.filter(name__icontains=search_query)

    context = {
        'brands': brands,
        'search_query': search_query,
    }
    return render(request, 'inventory/brand_list.html', context)


@login_required
def brand_create(request):
    """Create new brand (Admin/Manager only)"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('inventory:brand_list')

    if request.method == 'POST':
        form = BrandForm(request.POST, request.FILES)
        if form.is_valid():
            brand = form.save()
            messages.success(request, f'Brand "{brand.name}" created successfully!')
            return redirect('inventory:brand_list')
    else:
        form = BrandForm()

    context = {'form': form}
    return render(request, 'inventory/brand_create.html', context)
