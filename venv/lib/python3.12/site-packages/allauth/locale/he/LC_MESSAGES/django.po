# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-06-15 10:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hebrew <https://hosted.weblate.org/projects/allauth/django-"
"allauth/he/>\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Weblate 5.6-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "חשבון זה אינו פעיל כעת."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "לא ניתן להסיר את כתובת האימייל הראשית שלך."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "כתובת אימייל זו כבר משויכת לחשבון זה."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "כתובת האימייל ו/או הסיסמה אינם נכונים."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "שם המשתמש ו/או הסיסמה אינם נכונים."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "משתמש אחר כבר רשום עם כתובת אימייל זו."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "אנא הזן את הסיסמה הנוכחית."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "קוד שגוי."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "סיסמה שגויה."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "מפתח פגום או פג תוקף."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "אסימון פגום."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "אסימון איפוס הסיסמה אינו תקין."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "לא ניתן להוסיף יותר מ-%d כתובות אימייל."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "משתמש אחר כבר רשום עם כתובת אימייל זו."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "יותר מדי ניסיונות התחברות כושלים. אנא נסה שוב מאוחר יותר."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "כתובת אימייל זו אינה משויכת לאף חשבון"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "כתובת אימייל זו אינה משויכת לאף חשבון"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "עליך לאמת את כתובת האימייל הראשית שלך."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "לא ניתן להשתמש בשם משתמש זה. אנא בחר שם משתמש אחר."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "שם המשתמש ו/או הסיסמה אינם נכונים."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "להשתמש בסיסמה"

#: account/adapter.py:787
#, fuzzy
#| msgid "Use your authenticator app"
msgid "Use authenticator app or code"
msgstr "להשתמש ביישומון אימות"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
#, fuzzy
#| msgid "secret key"
msgid "Use a security key"
msgstr "מפתח סודי"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "סמן את כתובות האימייל שנבחרו כמאומתות."

#: account/apps.py:11
msgid "Accounts"
msgstr "חשבונות"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "יש להזין את אותה הסיסמה פעמיים."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "סיסמה"

#: account/forms.py:100
msgid "Remember Me"
msgstr "זכור אותי"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "כתובת אימייל"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "אימייל"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "שם משתמש"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "כניסה"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "שם משתמש או אימייל"

#: account/forms.py:156
msgid "Username or email"
msgstr "שם משתמש או אימייל"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "שם משתמש או אימייל"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "אימייל (לא חובה)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "שכחת את סיסמתך?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "אימייל (שוב)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "אישור כתובת אימייל"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "אימייל (לא חובה)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "אימייל (לא חובה)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "יש להזין את אותו האימייל פעמיים."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "סיסמה (שוב)"

#: account/forms.py:645
msgid "Current Password"
msgstr "סיסמה נוכחית"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "סיסמה חדשה"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "סיסמה חדשה (שוב)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "קוד"

#: account/models.py:26
msgid "user"
msgstr "משתמש"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "כתובת אימייל"

#: account/models.py:34
msgid "verified"
msgstr "מאומת"

#: account/models.py:35
msgid "primary"
msgstr "ראשי"

#: account/models.py:41
msgid "email addresses"
msgstr "כתובות אימייל"

#: account/models.py:151
msgid "created"
msgstr "נוצר"

#: account/models.py:152
msgid "sent"
msgstr "נשלח"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "מפתח"

#: account/models.py:158
msgid "email confirmation"
msgstr "אישור באימייל"

#: account/models.py:159
msgid "email confirmations"
msgstr "אישורים בדואל"

#: headless/apps.py:7
msgid "Headless"
msgstr "Headless"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr "לא ניתן להוסיף כתובת אימייל לחשבון שמוגן באימות דו-שלבי."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "לא ניתן לבטל אימות דו-שלבי."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr "לא ניתן ליצור קודי שחזור מבלי שאימות דו-שלבי יהיה מופעל."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr "לא ניתן להפעיל אימות דו-שלבי לפני שאימתת את כתובת האימייל."

#: mfa/adapter.py:141
#, fuzzy
#| msgid "secret key"
msgid "Master key"
msgstr "מפתח סודי"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr ""

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr ""

#: mfa/apps.py:9
msgid "MFA"
msgstr "אימות רב-שלבי"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "קודי שחזור"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "מאמת TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr ""

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "קוד אימות"

#: mfa/webauthn/forms.py:59
#, fuzzy
#| msgid "Password"
msgid "Passwordless"
msgstr "סיסמה"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"קיים כבר חשבון עם כתובת אימייל זו. אנא התחבר לחשבון זה, ואז קשר את חשבון %s "
"שלך."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "אסימון פגום."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "לא נבחרה סיסמה לחשבונך."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "לא נמצאו כתובות אימייל מאומתות לחשבונך."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "לא ניתן לנתק את חשבון הצד-השלישי האחרון שנשאר."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "חשבון חברתי זה כבר מקושר למשתמש אחר."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "חשבונות חברתיים"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "ספק"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "מזהה ספק"

#: socialaccount/models.py:56
msgid "name"
msgstr "שם"

#: socialaccount/models.py:58
msgid "client id"
msgstr "מזהה לקוח"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "מזהה יישום, או מפתח צרכן"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "מפתח סודי"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "סוד API, סוד לקוח או סוד צרכן"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "מפתח"

#: socialaccount/models.py:81
msgid "social application"
msgstr "יישום חברתי"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "יישומים חברתיים"

#: socialaccount/models.py:117
msgid "uid"
msgstr "מזהה ייחודי"

#: socialaccount/models.py:119
msgid "last login"
msgstr "התחברות אחרונה"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "תאריך הצטרפות"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "מידע נוסף"

#: socialaccount/models.py:125
msgid "social account"
msgstr "חשבון חברתי"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "חשבונות חברתיים"

#: socialaccount/models.py:160
msgid "token"
msgstr "אסימון"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) או אסימון גישה (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "סוד אסימון"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) או אסימון רענון (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "פג תוקף בתאריך"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "אסימון יישום חברתי"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "אסימוני יישום חברתי"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "מידע פרופיל שגוי"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "כניסה"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "ביטול"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr ""

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "אין אסימון בקשה שמור עבור \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "אין אסימון גישה שמור עבור \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "אין גישה למשאב פרטי ב-\"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr ""

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "חשבון לא פעיל"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "חשבון זה אינו פעיל."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr "שלחנו קוד ל-%(email_link)s. הקוד יפוג בקרוב, אז נא להזין אותו בהקדם."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "אמת"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "בקש קוד"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "אימות גישה"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "אנא הזדהה מחדש כדי להגן על חשבונך."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "אפשרויות חלופיות"

#: templates/account/confirm_email_verification_code.html:5
#, fuzzy
#| msgid "Email Confirmation"
msgid "Email Verification"
msgstr "אישור באימייל"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr ""

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "כתובת אימייל"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "כניסה"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "הזן קוד כניסה"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "איפוס סיסמה"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "איפוס סיסמה"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "שלח אימייל אימות מחדש"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Re-send Verification"
msgid "Enter Phone Verification Code"
msgstr "שלח אימייל אימות מחדש"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "כתובות אימייל"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "כתובות האימייל הבאות משויכות לחשבונך:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "מאומת"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "לא מאומת"

#: templates/account/email.html:34
msgid "Primary"
msgstr "ראשי"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "הפוך לראשי"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "שלח אימייל אימות מחדש"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "הסר"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "הוסף כתובת אימייל"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "הוסף אימייל"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "האם ברצונך להסיר את כתובות האימייל המסומנות?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "החשבון כבר קיים"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "שלום מ%(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"תודה שהשתמשת באתר %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "את/ה מקבל/ת אימייל עקב השינוי הבא שבוצע בחשבונך:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"אם אינך מזהה את השינוי הזה, נא לנקוט מיד באמצעי אבטחה נאותים. השינוי בחשבונך "
"מקורו ב:\n"
"\n"
"- כתובת IP: %(ip)s\n"
"- דפדפן: %(user_agent)s\n"
"- תאריך: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "האימייל שלך השתנה מ-%(from_email)s ל-%(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "אימייל השתנה"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "האימייל שלך אושר."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "אישור באימייל"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this email because user %(user_display)s has given your "
#| "email address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"את/ה מקבל/ת מייל זה מכיוון שהמשתמש %(user_display)s השתמש בכתובת האימייל שלך "
"כדי ליצור חשבון ב-%(site_domain)s.\n"
"\n"
"כדי לאמת זאת, לחץ על הקישור %(activate_url)s."

#: templates/account/email/email_confirmation_message.txt:7
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr "קוד הכניסה שלך מופיע למטה. אנא הזן/י אותו בחלון הדפדפן שפתוח."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "אנא אמת את כתובת האימייל שלך"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "כתובת האימייל %(deleted_email)s הוסרה מחשבונך."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "אימייל הוסר"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr "קוד הכניסה שלך מופיע למטה. אנא הזן/י אותו בחלון הדפדפן שפתוח."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "ניתן להתעלם בבטחה ממייל זה אם לא יזמת פעולה זו."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "קוד כניסה"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "סיסמתך שונתה."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "סיסמה שונתה"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr "קוד הכניסה שלך מופיע למטה. אנא הזן/י אותו בחלון הדפדפן שפתוח."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "איפוס סיסמה"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"מייל זה נשלח אליך כיוון שאת/ה או מישהו אחר ביקש לאפס את הסיסמה לחשבונך.\n"
"במידה ולא ביקשת איפוס סיסמה ניתן להתעלם ממייל זה ללא חשש. לחץ על הקישור מטה "
"לאיפוס סיסמתך."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "במידת ושכחת, שם המשתמש שלך הוא %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "מייל איפוס סיסמה"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "סיסמתך אופסה."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "סיסמתך הוגדרה."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "הגדרת סיסמה"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"הודעה זו נשלחה אליך כי את/ה, או מישהו אחר, ניסה לגשת לחשבון עם כתובת האימייל "
"%(email)s. עם זאת, אין לנו רישום של חשבון כזה במסד הנתונים שלנו."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "אם זה היית את/ה, תוכל/י להירשם לחשבון באמצעות הקישור למטה."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "חשבון לא ידוע"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "כתובת אימייל"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "כתובת אימייל נוכחית"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "משתנה ל"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "כתובת האימייל שלך עדיין ממתינה לאימות."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "בטל שינוי"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "שנה ל"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "שנה אימייל"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "אימות כתובת אימייל"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"אנא אמת ש<a href=\"mailto:%(email)s\">%(email)s</a> היא כתובת האימייל של "
"המשתמש %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "לא ניתן לאשר את %(email)s מכיוון שהיא כבר אושרה על ידי חשבון אחר."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"קישור זה לאימות כתובת אימייל פג תוקף או שאינו תקין. יש <a "
"href=\"%(email_url)s\">להנפיק בקשה חדשה לאימות כתובת אימייל</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "אם לא נרשמת לחשבון בעבר, אנא %(link)sהרשם%(end_link)s תחילה."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr ""

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "שלחו לי קוד כניסה למייל"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "יציאה"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "האם אתה בטוח שברצונך לצאת?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "לא ניתן להסיר את כתובת האימייל הראשית שלך (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "מייל אימות נשלח ל %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "כתובת האימייל %(email)s אומתה בהצלחה."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "כתובת האימייל %(email)s הוסרה."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "מחובר בהצלחה כ %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "התנתקת מהחשבון."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "קוד כניסה נשלח במייל ל-%(email)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "הסיסמה שונתה בהצלחה."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "הסיסמה נקבעה בהצלחה."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "קוד כניסה נשלח במייל ל-%(email)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "כתובת אימייל ראשית הוגדרה."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "החלפת סיסמה"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "שכחת סיסמה?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"שכחת את סיסמתך? הזן את כתובת האימייל שלך כאן, ונשלח לך מייל לאיפוס הסיסמה."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "אפס את הסיסמה"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "אנא צור איתנו קשר אם אתה מתקשה לאפס את הסיסמה."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"שלחנו לך מייל. אם לא קיבלת אותו, בדוק בתיקיית הספאם שלך. אחרת פנה אלינו אם "
"לא תקבל אותו תוך מספר דקות."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "אסימון פגום"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"הקישור לאיפוס הסיסמה אינו תקין, כנראה מכיוון שכבר נעשה בו שימוש.  לחץ כאן "
"לבקשת <a href=\"%(passwd_reset_url)s\">איפוס סיסמה</a> מחדש."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "סיסמתך שונתה."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "קביעת סיסמה"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "שנה ל"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current Password"
msgid "Current phone"
msgstr "סיסמה נוכחית"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "כתובת האימייל שלך עדיין ממתינה לאימות."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "הזינו סיסמה:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr "תקבל אימייל המכיל קוד מיוחד לכניסה ללא סיסמה."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "בקש קוד"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "אפשרויות כניסה אחרות"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "הרשמה"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "הרשמה"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "נרשמת בעבר? %(link)sכניסה למערכת%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr ""

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "הרשמה"

#: templates/account/signup_by_passkey.html:36
#, fuzzy
#| msgid "Other sign-in options"
msgid "Other options"
msgstr "אפשרויות כניסה אחרות"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "ההרשמה סגורה"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "אנו מצטערים, אך ההרשמה סגורה כעת."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "הערה"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "הנך מחובר כבר כ%(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "אזהרה:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"טרם שייכת כתובת אימייל לחשבונך. מומלץ לשייך כתובת אימייל על מנת לקבל התראות, "
"לאפס סיסמה וכו'."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "אשר את כתובת הדואל שלך"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"שלחנו לך אימייל לאימות. עקוב אחר הקישור שסופק כדי לסיים את תהליך ההרשמה. אם "
"אינך רואה את דוא\"ל האימות בתיבת הדואר הנכנס הראשית שלך, בדוק את תיקיית "
"הספאם. אנא צור איתנו קשר אם לא תקבל את הודעת האימות תוך מספר דקות."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"חלק זה באתר דורש מאיתנו לוודא כי הנך אכן מי שאתה טוען שאתה.\n"
"למטרה זו, אנו מבקשים כי תאמת בעלות על כתובת האימייל שלך. "

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"שלחנו אליך מייל למטרת אימות.\n"
"אנא ללץ על הקישור בתוך אימייל זה.\n"
"אנא צור עמנו קשר במידה והמייל לא התקבל תוך מספר דקות."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>הערה:</strong> אתה עדיין יכול <a href=\"%(email_url)s\">לשנות את "
"כתובת האימייל שלך </a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "קישורים לחשבון"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr ""

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr ""

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr ""

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr ""

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr ""

#: templates/mfa/email/webauthn_removed_message.txt:4
#, fuzzy
#| msgid "Your email has been confirmed."
msgid "A security key has been removed."
msgstr "האימייל שלך אושר."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr ""

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr ""

#: templates/mfa/index.html:62
msgid "Manage"
msgstr ""

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr ""

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:96
msgid "View"
msgstr ""

#: templates/mfa/index.html:102
msgid "Download"
msgstr ""

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr ""

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr ""

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr ""

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Add Security Key"
msgstr "מפתח סודי"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
#, fuzzy
#| msgid "secret key"
msgid "Remove Security Key"
msgstr "מפתח סודי"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
#, fuzzy
#| msgid "Are you sure you want to sign out?"
msgid "Are you sure you want to remove this security key?"
msgstr "האם אתה בטוח שברצונך לצאת?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:37
#, fuzzy
#| msgid "secret key"
msgid "Security key"
msgstr "מפתח סודי"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:41
#, fuzzy
#| msgid "Unverified"
msgid "Unspecified"
msgstr "לא מאומת"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Edit Security Key"
msgstr "מפתח סודי"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr ""

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Current Password"
msgid "Create Passkey"
msgstr "סיסמה נוכחית"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "נוצר"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "שגיאת התחברות לרשת חברתית"

#: templates/socialaccount/authentication_error.html:12
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "אירעה שגיאה במהלך ניסיון התחברות באמצעות חשבון הרשת החברתית שלך."

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "ניתן להתחבר לחשבונך באמצעות כל אחד מחשבונות צד שלישי הבאים:"

#: templates/socialaccount/connections.html:46
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "לא קיימים חשבונות רשת חברתית המקושרים לחשבון זה כרגע."

#: templates/socialaccount/connections.html:50
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "הוסף חשבון צד שלישי"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Connected"
msgstr "הוסף חשבון צד שלישי"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Disconnected"
msgstr "הוסף חשבון צד שלישי"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "התחברות בוטלה"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"ביקשת לבטל את ההתחברות לאתר זה באמצעות אחד מחשבונותיך הקיימים. במידה וטעית, "
"אנא המשך ל<a href=\"%(login_url)s\">התחברות</a>."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "החשבון החברתי קושר בהצלחה."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "חשבון חברתי זה נותק."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"אתה עומד להשתמש בחשבון %(provider_name)s שלך כדי\n"
"להתחבר ל%(site_name)s. לסיום, אנא מלא את הטופס הבא:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr ""

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Addresses"
msgid "IP Address"
msgstr "כתובות אימייל"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
#, fuzzy
#| msgid "Current Password"
msgid "Current"
msgstr "סיסמה נוכחית"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:92
msgid "session key"
msgstr ""

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "קישורים לחשבון"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "הסיסמה חייבת להיות באורך של לפחות {0} תווים."

#, fuzzy, python-format
#~| msgid ""
#~| "Hello from %(site_name)s!\n"
#~| "\n"
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "שלום מ%(site_name)s!\n"
#~ "\n"
#~ "מייל זה נשלח אליך כיוון שאתה או מישהו אחר ביקש סיסמה עבור חשבונך ב "
#~ "%(site_name)s.\n"
#~ "במידה ולא ביקשת איפוס סיסמה ניתן להתעלם ממייל זה ללא חשש. לחץ על הקישור "
#~ "מטה לאיפוס סיסמתך."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "כתובות האימייל הבאות משויכות לחשבונך:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "אימות כתובת אימייל"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "אנא הכנס עם אחד מהחשבונות הקיימים שלך.\n"
#~ "או, %(link)sהרשם%(end_link)s לחשבון %(site_name)s והתחבר:"

#~ msgid "or"
#~ msgstr "או"

#~ msgid "change password"
#~ msgstr "החלפת סיסמה"

#~ msgid "OpenID Sign In"
#~ msgstr "כניסה באמצעות OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "כתובת אימייל זו כבר משויכת לחשבון אחר."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr "המייל נשלח. אנא צור איתנו קשר אם הוא אינו מתקבל תוך מספר דקות."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "שם המשתמש ו/או הסיסמא אינם נכונים"

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "שם משתמש זה כבר תפוס, אנא ציין שם אחר"

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "כניסה"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "אישרת בהצלחה כי <a href=\"mailto:%(email)s\">%(email)s</a> הנה כתובת דואר "
#~ "אלקטרוני עבור המשתמש %(user_display)s."

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "דואל אישור נשלח אל %(email)s"

#~ msgid "Delete Password"
#~ msgstr "מחיקת סיסמא"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "אתה רשאי למחוק את סיסמאתך כיוון שהנך מחובר באמצעות OpenID"

#~ msgid "delete my password"
#~ msgstr "מחק סיסמא"

#~ msgid "Password Deleted"
#~ msgstr "הסיסמא נמחקה"
