{% extends 'base.html' %} {% block title %}{{ product.title }} - YalaOffice{%
endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'inventory:product_list' %}">Products</a>
          </li>
          <li class="breadcrumb-item active">{{ product.title }}</li>
        </ol>
      </nav>
      {% if user.is_admin or user.is_manager %}
      <div class="btn-group">
        <a
          href="{% url 'inventory:product_edit' product.id %}"
          class="btn btn-warning"
        >
          <i class="fas fa-edit"></i> Edit
        </a>
        <a
          href="{% url 'inventory:product_delete' product.id %}"
          class="btn btn-danger"
        >
          <i class="fas fa-trash"></i> Delete
        </a>
      </div>
      {% endif %}
    </div>
  </div>
</div>

<div class="row">
  <!-- Product Images -->
  <div class="col-md-6">
    <div class="card">
      <div class="card-body">
        {% if product.main_image %}
        <img
          src="{{ product.main_image.url }}"
          class="img-fluid rounded mb-3"
          alt="{{ product.title }}"
        />
        {% else %}
        <div
          class="bg-light d-flex align-items-center justify-content-center rounded mb-3"
          style="height: 400px"
        >
          <i class="fas fa-image fa-5x text-muted"></i>
        </div>
        {% endif %}

        <!-- Additional Images -->
        {% if additional_images %}
        <div class="row">
          {% for image in additional_images %}
          <div class="col-3 mb-2">
            <img
              src="{{ image.image.url }}"
              class="img-fluid rounded"
              alt="{{ image.alt_text }}"
            />
          </div>
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Product Information -->
  <div class="col-md-6">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-start mb-3">
          <h2>{{ product.title }}</h2>
          <div>
            {% if product.is_featured %}
            <span class="badge bg-warning">Featured</span>
            {% endif %} {% if product.is_active %}
            <span class="badge bg-success">Active</span>
            {% else %}
            <span class="badge bg-secondary">Inactive</span>
            {% endif %}
          </div>
        </div>

        <p class="text-muted">{{ product.description }}</p>

        <!-- Pricing -->
        <div class="row mb-4">
          <div class="col-6">
            <div class="card bg-light">
              <div class="card-body text-center">
                <h6 class="card-title">Normal Price</h6>
                <h4 class="text-primary">{{ product.normal_price }} Dh</h4>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="card bg-light">
              <div class="card-body text-center">
                <h6 class="card-title">Reseller Price</h6>
                <h4 class="text-success">{{ product.reseller_price }} Dh</h4>
                <small class="text-muted">
                  Save {{
                  product.normal_price|floatformat:2|add:"-"|add:product.reseller_price|floatformat:2
                  }} Dh
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- Product Details -->
        <div class="table-responsive mb-4">
          <table class="table table-sm">
            <tr>
              <th width="30%">SKU:</th>
              <td>{{ product.sku }}</td>
            </tr>
            <tr>
              <th>Category:</th>
              <td>
                {{ product.category.name }} {% if product.subcategory %} > {{
                product.subcategory.name }} {% endif %}
              </td>
            </tr>
            {% if product.brand %}
            <tr>
              <th>Brand:</th>
              <td>{{ product.brand.name }}</td>
            </tr>
            {% endif %} {% if product.weight %}
            <tr>
              <th>Weight:</th>
              <td>{{ product.weight }} kg</td>
            </tr>
            {% endif %} {% if product.dimensions %}
            <tr>
              <th>Dimensions:</th>
              <td>{{ product.dimensions }}</td>
            </tr>
            {% endif %}
            <tr>
              <th>Created:</th>
              <td>{{ product.created_at|date:"M d, Y H:i" }}</td>
            </tr>
            <tr>
              <th>Last Updated:</th>
              <td>{{ product.updated_at|date:"M d, Y H:i" }}</td>
            </tr>
          </table>
        </div>

        <!-- Action Buttons -->
        {% if user.is_client or user.is_reseller %}
        <div class="d-grid gap-2">
          <button
            class="btn btn-success btn-lg"
            onclick="addToCart({{ product.id }})"
          >
            <i class="fas fa-cart-plus"></i> Add to Cart {% if user.is_reseller
            %} ({{ product.reseller_price }} Dh) {% else %} ({{
            product.normal_price }} Dh) {% endif %}
          </button>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Stock Information -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5><i class="fas fa-warehouse"></i> Stock Information</h5>
      </div>
      <div class="card-body">
        {% if stock_items %}
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Branch</th>
                <th>Available</th>
                <th>Reserved</th>
                <th>Total</th>
                <th>Min Stock</th>
                <th>Status</th>
                {% if user.is_admin or user.is_manager %}
                <th>Actions</th>
                {% endif %}
              </tr>
            </thead>
            <tbody>
              {% for stock in stock_items %}
              <tr>
                <td>
                  <strong>{{ stock.branch.name }}</strong><br />
                  <small class="text-muted">{{ stock.branch.city }}</small>
                </td>
                <td>
                  <span class="badge bg-primary"
                    >{{ stock.available_quantity }}</span
                  >
                </td>
                <td>
                  <span class="badge bg-warning"
                    >{{ stock.reserved_quantity }}</span
                  >
                </td>
                <td>
                  <span class="badge bg-info">{{ stock.quantity }}</span>
                </td>
                <td>{{ stock.minimum_stock }}</td>
                <td>
                  {% if stock.is_out_of_stock %}
                  <span class="badge bg-danger">Out of Stock</span>
                  {% elif stock.is_low_stock %}
                  <span class="badge bg-warning">Low Stock</span>
                  {% else %}
                  <span class="badge bg-success">In Stock</span>
                  {% endif %}
                </td>
                {% if user.is_admin or user.is_manager %}
                <td>
                  <a
                    href="{% url 'inventory:stock_update' stock.id %}"
                    class="btn btn-sm btn-outline-primary"
                  >
                    <i class="fas fa-edit"></i> Update
                  </a>
                </td>
                {% endif %}
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Stock Summary -->
        <div class="row mt-3">
          <div class="col-md-3">
            <div class="card bg-primary text-white">
              <div class="card-body text-center">
                <h5>{{ total_stock.total_quantity|default:0 }}</h5>
                <small>Total Stock</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-success text-white">
              <div class="card-body text-center">
                <h5>{{ total_stock.total_available|default:0 }}</h5>
                <small>Available</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-info text-white">
              <div class="card-body text-center">
                <h5>{{ stock_items|length }}</h5>
                <small>Branches</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-warning text-white">
              <div class="card-body text-center">
                <h5>
                  {% with low_stock_count=stock_items|length %} {% for stock in
                  stock_items %} {% if stock.is_low_stock %}{{
                  forloop.counter0|add:1 }}{% endif %} {% endfor %} {% endwith
                  %}
                </h5>
                <small>Low Stock Alerts</small>
              </div>
            </div>
          </div>
        </div>
        {% else %}
        <div class="text-center py-4">
          <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
          <h5>No Stock Information</h5>
          <p class="text-muted">
            This product has no stock records in any branch.
          </p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  function addToCart(productId) {
    const quantity = document.getElementById("quantity-input")
      ? parseInt(document.getElementById("quantity-input").value)
      : 1;

    fetch("/orders/cart/add/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCookie("csrftoken"),
      },
      body: JSON.stringify({
        product_id: productId,
        quantity: quantity,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          showMessage(data.message, "success");
          updateCartBadge(data.cart_total_items);
        } else {
          showMessage(data.message, "danger");
        }
      })
      .catch((error) => {
        console.error("Error:", error);
        showMessage("An error occurred while adding to cart", "danger");
      });
  }

  function showMessage(message, type) {
    const toast = document.createElement("div");
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText =
      "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 3000);
  }

  function updateCartBadge(count) {
    const badge = document.querySelector(".cart-badge");
    if (badge) {
      badge.textContent = count;
      badge.style.display = count > 0 ? "inline" : "none";
    }
  }

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== "") {
      const cookies = document.cookie.split(";");
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === name + "=") {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }
</script>
{% endblock %}
