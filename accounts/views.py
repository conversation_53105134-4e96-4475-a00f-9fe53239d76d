from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q
from .models import User, Branch, UserProfile
from .forms import UserRegistrationForm, UserProfileForm, BranchForm


def dashboard(request):
    """Main dashboard view based on user role"""
    if not request.user.is_authenticated:
        return redirect('account_login')

    context = {
        'user': request.user,
        'user_role': request.user.role,
    }

    if request.user.is_admin:
        return render(request, 'accounts/admin_dashboard.html', context)
    elif request.user.is_manager:
        return render(request, 'accounts/manager_dashboard.html', context)
    elif request.user.is_delivery:
        return render(request, 'accounts/delivery_dashboard.html', context)
    elif request.user.is_reseller:
        return render(request, 'accounts/reseller_dashboard.html', context)
    else:  # Normal client
        return render(request, 'accounts/client_dashboard.html', context)


@login_required
def profile(request):
    """User profile view and edit"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('accounts:profile')
    else:
        form = UserProfileForm(instance=profile)

    context = {
        'form': form,
        'profile': profile,
    }
    return render(request, 'accounts/profile.html', context)


@login_required
def user_list(request):
    """List all users (Admin only)"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    users = User.objects.all().select_related('profile')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        users = users.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(phone__icontains=search_query)
        )

    # Filter by role
    role_filter = request.GET.get('role', '')
    if role_filter:
        users = users.filter(role=role_filter)

    # Pagination
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'role_filter': role_filter,
        'user_roles': User.USER_ROLES,
    }
    return render(request, 'accounts/user_list.html', context)


@login_required
def user_create(request):
    """Create new user (Admin only)"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, f'User {user.get_full_name()} created successfully!')
            return redirect('accounts:user_list')
    else:
        form = UserRegistrationForm()

    context = {'form': form}
    return render(request, 'accounts/user_create.html', context)


@login_required
def user_detail(request, user_id):
    """User detail view"""
    if not request.user.is_admin and request.user.id != user_id:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    user = get_object_or_404(User, id=user_id)
    profile = getattr(user, 'profile', None)

    context = {
        'user_obj': user,
        'profile': profile,
    }
    return render(request, 'accounts/user_detail.html', context)


@login_required
def branch_list(request):
    """List all branches"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    branches = Branch.objects.all().select_related('manager')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        branches = branches.filter(
            Q(name__icontains=search_query) |
            Q(city__icontains=search_query) |
            Q(address__icontains=search_query)
        )

    context = {
        'branches': branches,
        'search_query': search_query,
    }
    return render(request, 'accounts/branch_list.html', context)


@login_required
def branch_create(request):
    """Create new branch (Admin only)"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = BranchForm(request.POST)
        if form.is_valid():
            branch = form.save()
            messages.success(request, f'Branch {branch.name} created successfully!')
            return redirect('accounts:branch_list')
    else:
        form = BranchForm()

    context = {'form': form}
    return render(request, 'accounts/branch_create.html', context)
