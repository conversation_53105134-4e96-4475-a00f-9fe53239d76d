from django import forms
from django.core.exceptions import ValidationError
from .models import Order, PromoCode
from accounts.models import Branch


class CheckoutForm(forms.Form):
    """Form for checkout process"""
    
    # Delivery Information
    delivery_address = forms.CharField(
        max_length=500,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Enter your full delivery address'
        }),
        label='Delivery Address'
    )
    
    delivery_city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'City'
        }),
        label='City'
    )
    
    delivery_phone = forms.CharField(
        max_length=15,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Phone number for delivery'
        }),
        label='Phone Number'
    )
    
    delivery_notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Special delivery instructions (optional)'
        }),
        label='Delivery Notes'
    )
    
    # Branch Selection
    branch = forms.ModelChoiceField(
        queryset=Branch.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='Select Branch',
        help_text='Choose the branch closest to your delivery location'
    )
    
    # Payment Method
    payment_method = forms.ChoiceField(
        choices=Order.PAYMENT_METHODS,
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        label='Payment Method'
    )
    
    # Terms and Conditions
    accept_terms = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='I accept the terms and conditions'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['branch'].empty_label = "Select a branch"


class PromoCodeForm(forms.Form):
    """Form for applying promo codes"""
    
    code = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter promo code',
            'id': 'promo-code-input'
        }),
        label='Promo Code'
    )
    
    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            try:
                promo_code = PromoCode.objects.get(code=code.upper())
                if not promo_code.is_valid():
                    raise ValidationError("This promo code is not valid or has expired.")
                return code.upper()
            except PromoCode.DoesNotExist:
                raise ValidationError("Invalid promo code.")
        return code


class OrderFilterForm(forms.Form):
    """Form for filtering orders"""
    
    STATUS_CHOICES = [('', 'All Status')] + list(Order.STATUS_CHOICES)
    PAYMENT_STATUS_CHOICES = [('', 'All Payment Status')] + list(Order.PAYMENT_STATUS)
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by order number, customer name...'
        })
    )
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    payment_status = forms.ChoiceField(
        choices=PAYMENT_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    branch = forms.ModelChoiceField(
        queryset=Branch.objects.filter(is_active=True),
        required=False,
        empty_label="All Branches",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


class OrderStatusUpdateForm(forms.ModelForm):
    """Form for updating order status"""
    
    class Meta:
        model = Order
        fields = ['status', 'payment_status']
        widgets = {
            'status': forms.Select(attrs={'class': 'form-control'}),
            'payment_status': forms.Select(attrs={'class': 'form-control'}),
        }
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Add notes about this status update...'
        }),
        label='Notes'
    )


class PromoCodeCreateForm(forms.ModelForm):
    """Form for creating promo codes"""
    
    class Meta:
        model = PromoCode
        fields = [
            'code', 'description', 'discount_type', 'discount_value',
            'minimum_order_amount', 'maximum_discount', 'usage_limit',
            'valid_from', 'valid_until', 'is_active'
        ]
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'PROMO2024'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'discount_type': forms.Select(attrs={'class': 'form-control'}),
            'discount_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'minimum_order_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'maximum_discount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'usage_limit': forms.NumberInput(attrs={'class': 'form-control'}),
            'valid_from': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'valid_until': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            code = code.upper()
            # Check if code already exists (excluding current instance if editing)
            existing = PromoCode.objects.filter(code=code)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError("A promo code with this code already exists.")
        
        return code
    
    def clean(self):
        cleaned_data = super().clean()
        valid_from = cleaned_data.get('valid_from')
        valid_until = cleaned_data.get('valid_until')
        discount_type = cleaned_data.get('discount_type')
        discount_value = cleaned_data.get('discount_value')
        maximum_discount = cleaned_data.get('maximum_discount')
        
        # Validate date range
        if valid_from and valid_until and valid_from >= valid_until:
            raise ValidationError("Valid until date must be after valid from date.")
        
        # Validate discount value for percentage type
        if discount_type == 'percentage' and discount_value and discount_value > 100:
            raise ValidationError("Percentage discount cannot be more than 100%.")
        
        # Validate maximum discount for percentage type
        if discount_type == 'percentage' and maximum_discount and not maximum_discount > 0:
            raise ValidationError("Maximum discount must be greater than 0 for percentage discounts.")
        
        return cleaned_data


class QuickOrderForm(forms.Form):
    """Form for quick order creation (for managers)"""
    
    customer_email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Customer email address'
        }),
        label='Customer Email'
    )
    
    products = forms.CharField(
        widget=forms.HiddenInput(),
        help_text='JSON string of products and quantities'
    )
    
    branch = forms.ModelChoiceField(
        queryset=Branch.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='Branch'
    )
    
    payment_method = forms.ChoiceField(
        choices=Order.PAYMENT_METHODS,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='Payment Method'
    )
    
    delivery_address = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Delivery address'
        }),
        label='Delivery Address'
    )
    
    delivery_city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'City'
        }),
        label='City'
    )
    
    delivery_phone = forms.CharField(
        max_length=15,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Phone number'
        }),
        label='Phone Number'
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Order notes (optional)'
        }),
        label='Notes'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['branch'].empty_label = "Select branch"
    
    def clean_products(self):
        products_json = self.cleaned_data.get('products')
        try:
            import json
            products = json.loads(products_json)
            if not isinstance(products, list) or not products:
                raise ValidationError("At least one product must be selected.")
            
            for product in products:
                if not all(key in product for key in ['id', 'quantity']):
                    raise ValidationError("Invalid product data format.")
                
                if product['quantity'] <= 0:
                    raise ValidationError("Product quantity must be greater than 0.")
            
            return products
        except (json.JSONDecodeError, TypeError):
            raise ValidationError("Invalid product data format.")


class CartUpdateForm(forms.Form):
    """Form for updating cart items"""
    
    quantity = forms.IntegerField(
        min_value=1,
        max_value=999,
        widget=forms.NumberInput(attrs={
            'class': 'form-control form-control-sm',
            'style': 'width: 80px;'
        })
    )
