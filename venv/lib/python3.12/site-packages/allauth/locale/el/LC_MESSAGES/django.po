# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-04-29 20:32+0000\n"
"Last-Translator: GiannosOB <<EMAIL>>\n"
"Language-Team: Greek <https://hosted.weblate.org/projects/allauth/django-"
"allauth/el/>\n"
"Language: el\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Αυτός ο λογαριασμός είναι ανενεργός."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr ""
"Δεν μπορείτε να καταργήσετε την κύρια διεύθυνση ηλεκτρονικού ταχυδρομείου "
"σας."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Αυτό το e-mail χρησιμοποιείται ήδη από αυτό το λογαριασμό."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Η διέυθυνση e-mail ή/και το συνθηματικό που δόθηκαν δεν είναι σωστά."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr ""
"Ο αριθμός τηλεφώνου ή/και ο κωδικός πρόσβασης που δηλώσατε δεν είναι σωστοί."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Υπάρχει ήδη εγγεγραμμένος χρήστης με αυτό το e-mail."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Παρακαλώ γράψτε το τρέχον συνθηματικό σας."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Λανθασμένος κωδικός."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Λάθος κωδικός πρόσβασης."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Μη έγκυρο ή ληγμένο κλειδί."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Μη έγκυρη σύνδεση."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Το κουπόνι επαναφοράς του συνθηματικού δεν ήταν έγκυρο."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Δεν μπορείτε να προσθέσετε περισσότερες από %d διευθύνσεις email."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Υπάρχει ήδη εγγεγραμμένος χρήστης με αυτό το e-mail."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Πολλές αποτυχημένες προσπάθειες σύνδεσης. Προσπαθήστε ξανά αργότερα."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr ""
"Η διεύθυνση ηλεκτρονικού ταχυδρομείου δεν αποδίδεται σε κανένα λογαριασμό "
"χρήστη."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Ο αριθμός τηλεφώνου δεν έχει εκχωρηθεί σε κανένα λογαριασμό χρήστη."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Η πρωτεύον διεύθυνση e-mail πρέπει να επιβεβαιωθεί."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Δεν μπορεί να χρησιμοποιηθεί αυτό το όνομα χρήστη. Δοκιμάστε άλλο."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Το όνομα χρήστη ή/και το συνθηματικό που δόθηκαν δεν είναι σωστά."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Παρακαλώ επιλέξτε μόνο ένα."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Η νέα τιμή πρέπει να είναι διαφορετική από την τρέχουσα."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Χρησιμοποιήστε τον κωδικό πρόσβασής σας"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Χρήση εφαρμογής ή κωδικού πιστοποίησης ταυτότητας"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Χρήση κλειδιού ασφαλείας"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Σημείωση επιλεγμένων διευθύνσεων email όπως επαληθεύτηκε"

#: account/apps.py:11
msgid "Accounts"
msgstr "Λογαριασμοί"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Εισάγετε έναν αριθμό τηλεφώνου, συμπεριλαμβανομένου του κωδικού χώρας (π.χ. "
"+1 για τις ΗΠΑ)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Τηλέφωνο"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Πρέπει να δοθεί το ίδιο συνθηματικό κάθε φορά."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Συνθηματικό"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Αυτόματη Σύνδεση"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Διεύθυνση e-mail"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Όνομα χρήστη"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Σύνδεση"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Όνομα χρήστη, email ή τηλέφωνο"

#: account/forms.py:156
msgid "Username or email"
msgstr "Όνομα χρήστη ή e-mail"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Όνομα χρήστη ή τηλέφωνο"

#: account/forms.py:160
msgid "Email or phone"
msgstr "Ηλεκτρονικό ταχυδρομείο ή τηλέφωνο"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Ξέχασες τον κωδικό σου?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "Ηλεκτρονικό ταχυδρομείο (ξανά)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Επιβεβαίωση διεύθυνσης ηλεκτρονικού ταχυδρομείου"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-mail (προαιρετικό)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Όνομα χρήστη (προαιρετικό)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Πρέπει να πληκτρολογείτε το ίδιο email κάθε φορά."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Συνθηματικό (επιβεβαίωση)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Τρέχον συνθηματικό"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Νέο συνθηματικό"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Νέο συνθηματικό (επιβεβαίωση)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Κωδικός"

#: account/models.py:26
msgid "user"
msgstr "χρήστης"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "διεύθυνση e-mail"

#: account/models.py:34
msgid "verified"
msgstr "επαληθευμένο"

#: account/models.py:35
msgid "primary"
msgstr "πρωτεύον"

#: account/models.py:41
msgid "email addresses"
msgstr "διευθύνσεις e-mail"

#: account/models.py:151
msgid "created"
msgstr "δημιουργήθηκε"

#: account/models.py:152
msgid "sent"
msgstr "απστάλει"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "κλειδί"

#: account/models.py:158
msgid "email confirmation"
msgstr "e-mail επιβεβαίωσης"

#: account/models.py:159
msgid "email confirmations"
msgstr "e-mail επιβεβαίωσης"

#: headless/apps.py:7
msgid "Headless"
msgstr "Ακέφαλος"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Δεν μπορείτε να προσθέσετε μια διεύθυνση ηλεκτρονικού ταχυδρομείου σε έναν "
"λογαριασμό που προστατεύεται με έλεγχο ταυτότητας δύο παραγόντων."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Δεν μπορείτε να απενεργοποιήσετε τον έλεγχο ταυτότητας δύο παραγόντων."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Δεν μπορείτε να δημιουργήσετε κωδικούς ανάκτησης χωρίς να έχετε "
"ενεργοποιήσει τον έλεγχο ταυτότητας δύο παραγόντων."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Δεν μπορείτε να ενεργοποιήσετε τον έλεγχο ταυτότητας δύο παραγόντων μέχρι να "
"επαληθεύσετε τη διεύθυνση ηλεκτρονικού ταχυδρομείου σας."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Βασικό κλειδί"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Εφεδρικό κλειδί"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Αριθμός κλειδιού {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Κωδικοί ανάκτησης"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Αυθεντικοποιητής TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Κωδικός πιστοποίησης"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Χωρίς κωδικό πρόσβασης"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Η ενεργοποίηση της λειτουργίας χωρίς κωδικό πρόσβασης σάς επιτρέπει να "
"συνδεθείτε χρησιμοποιώντας μόνο αυτό το κλειδί, αλλά επιβάλλει πρόσθετες "
"απαιτήσεις, όπως βιομετρικά στοιχεία ή προστασία με PIN."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Υπάρχει ήδη ένας λογαριασμός με αυτή τη διεύθυνση ηλεκτρονικού ταχυδρομείου. "
"Συνδεθείτε πρώτα σε αυτόν το λογαριασμό και, στη συνέχεια, συνδέστε το "
"λογαριασμό %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Μη έγκυρη ένδειξη."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Δεν έχει οριστεί συνθηματικό στον λογαριασμό σας."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Δεν έχει επιβεβαιωθεί κανένα e-mail του λογαριασμού σας."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Δεν μπορείτε να αποσυνδέσετε τον τελευταίο λογαριασμό τρίτων."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr ""
"Ο λογαριασμός κοινωνικών μέσων είναι ήδη συνδεδεμένος με διαφορετικό λΟ "
"λογαριασμός τρίτου μέρους είναι ήδη συνδεδεμένος με διαφορετικό λογαριασμό."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Λογαριασμοί Κοινωνικών Μέσων"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "πάροχος"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID παρόχου"

#: socialaccount/models.py:56
msgid "name"
msgstr "όνομα"

#: socialaccount/models.py:58
msgid "client id"
msgstr "id πελάτη"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "App ID ή consumer key(κλειδί καταναλωτή)"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "μυστικό κλειδί"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret, ή consumer secret"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Κλειδί"

#: socialaccount/models.py:81
msgid "social application"
msgstr "εφαρμογή κοινωνικών μέσων"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "εφαρμογές κοινωνικών μέσων"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "τελευταία σύνδεση"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "ημερομηνία εγγραφής"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "έξτρα δεδομένα"

#: socialaccount/models.py:125
msgid "social account"
msgstr "λογαριασμός κοινωνικών μέσων"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "λογαριασμοί κοινωνικών μέσων"

#: socialaccount/models.py:160
msgid "token"
msgstr "κουπόνι"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ή access token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "διακριτικό μυστικό"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ή refresh token (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "λήγει στις"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token (κουπόνι) εφαρμογής κοινωνικών μέσων"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "tokens (κουπόνια) εφαρμογής κοινωνικών μέσων"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Άκυρα δεδομένα προφίλ"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Σύνδεση"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Ακύρωση"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Μη έγκυρη απάντηση κατά τη λήψη του συμβόλου αίτησης από το «%s». Η απάντηση "
"ήταν: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Μη-έγκυρη απάντηση κατά την απόκτηση κουπονιού πρόσβασης από \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Δεν υπάρχει αποθηκευμένο κουπόνι αιτήματος για \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Δεν υπάρχει αποθηκευμένο κουπόνι πρόσβασης για \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Αδύνατη πρόσβαση σε ιδιοτικούς πόρους στο \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Μη-έγκυρη απάντηση κατά την απόκτηση κουπονιού αιτήματος από \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Λογαριασμός Ανενεργός"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Αυτός ο λογαριασμός είναι ανενεργός."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Στείλαμε έναν κωδικό σε %(recipient)s). Ο κωδικός λήγει σύντομα, γι' αυτό "
"παρακαλούμε εισάγετε τον σύντομα."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Επιβεβαίωση"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Ζητήστε κωδικός"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Επιβεβαίωση πρόσβασης"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr ""
"Παρακαλείστε να επαληθεύσετε την ταυτότητά σας για να διασφαλίσετε το "
"λογαριασμό σας."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Εναλλακτικές επιλογές"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Επαλήθευση email"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Εισάγετε τον κωδικό επαλήθευσης email"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "διεύθυνση e-mail"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Εγγραφείτε"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Πληκτρολογήστε τον κωδικό εισόδου"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Επαναφορά Συνθηματικού"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Εισάγετε τον κωδικό επαναφοράς κωδικού πρόσβασης"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Επαλήθευση τηλεφώνου"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Εισάγετε τον κωδικό επαλήθευσης τηλεφώνου"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Διεύθυνση e-mail"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr ""
"Οι διευθύνσεις e-mail που ακολουθούν είναι συσχετισμένες με τον λογαριασμό "
"σας:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Εγκεκριμένος"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Μη-επιβεβαιωμένο"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Πρωτεύον"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Ορισμός ως Πρωτεύον"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Επανάληψη αποστολής Επαλήθευσης"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Αφαίρεση"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Προσθήκη διέυθυνσης e-mail"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Προσθήκη e-mail"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Θέλετε να αφαιρέσετε την επλεγμένη διεύθυνση e-mail?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Λαμβάνετε αυτό το μήνυμα ηλεκτρονικού ταχυδρομείου επειδή εσείς ή κάποιος "
"άλλος προσπάθησε να εγγραφεί σε ένα\n"
"λογαριασμό χρησιμοποιώντας τη διεύθυνση ηλεκτρονικού ταχυδρομείου:\n"
"\n"
"%(email)s\n"
"\n"
"Ωστόσο, ένας λογαριασμός που χρησιμοποιεί αυτή τη διεύθυνση ηλεκτρονικού "
"ταχυδρομείου υπάρχει ήδη.  Σε περίπτωση που έχετε\n"
"ξεχάσετε αυτό, παρακαλούμε χρησιμοποιήστε τη διαδικασία ξεχασμένου κωδικού "
"πρόσβασης για να ανακτήσετε\n"
"λογαριασμό σας:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Ο λογαριασμός υπάρχει ήδη"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Γεια σας από το %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Ευχαριστούμε που χρησιμοποιήσατε το %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Λαμβάνετε αυτό το μήνυμα επειδή έγινε η ακόλουθη αλλαγή στο λογαριασμό σας:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Εάν δεν αναγνωρίζετε αυτή την αλλαγή, τότε παρακαλούμε λάβετε αμέσως τα "
"κατάλληλα μέτρα ασφαλείας. Η αλλαγή στο λογαριασμό σας προέρχεται από:\n"
"\n"
"- Διεύθυνση IP: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Ημερομηνία: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Το email σας έχει αλλάξει από %(from_email)s σε %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Ηλεκτρονικό ταχυδρομείο άλλαξε"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Το email σας έχει επιβεβαιωθεί."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Επιβεβαίωση μέσω ηλεκτρονικού ταχυδρομείου"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Λαμβάνετε αυτό το μήνυμα ηλεκτρονικού ταχυδρομείου επειδή ο χρήστης "
"%(user_display)s έδωσε τη διεύθυνση ηλεκτρονικού ταχυδρομείου σας για την "
"εγγραφή ενός λογαριασμού στο %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ο κωδικός επαλήθευσης του email σας αναφέρεται παρακάτω. Παρακαλούμε "
"εισάγετε τον στο ανοιχτό παράθυρο του προγράμματος περιήγησης."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Για να επιβεβαιώσετε ότι αυτό είναι σωστό, μεταβείτε στο %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Παρακαλούμε να επιβεβαιώσετε την διεύθυνση e-mail σας"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""
"Η διεύθυνση ηλεκτρονικού ταχυδρομείου %(deleted_email)s έχει αφαιρεθεί από "
"το λογαριασμό σας."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Το ηλεκτρονικό ταχυδρομείο αφαιρέθηκε"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Ο κωδικός σύνδεσής σας παρατίθεται παρακάτω. Παρακαλούμε εισάγετε τον στο "
"ανοιχτό παράθυρο του προγράμματος περιήγησης."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Αυτό το μήνυμα μπορείτε να το αγνοήσετε με ασφάλεια, αν δεν προκαλέσατε "
"εσείς αυτή την ενέργεια."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Κωδικός εισόδου"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Ο κωδικός πρόσβασής σας έχει αλλάξει."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Ο κωδικός πρόσβασης άλλαξε"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ο κωδικός επαναφοράς του κωδικού πρόσβασης παρατίθεται παρακάτω. Παρακαλούμε "
"εισάγετε τον στο ανοιχτό παράθυρο του προγράμματος περιήγησης."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Επαναφορά κωδικού πρόσβασης"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Λαμβάνετε αυτό το μήνυμα ηλεκτρονικού ταχυδρομείου επειδή εσείς ή κάποιος "
"άλλος έχει ζητήσει επαναφορά κωδικού πρόσβασης για το λογαριασμό χρήστη "
"σας.\n"
"Μπορείτε να το αγνοήσετε με ασφάλεια εάν δεν ζητήσατε επαναφορά κωδικού "
"πρόσβασης. Κάντε κλικ στον παρακάτω σύνδεσμο για να επαναφέρετε τον κωδικό "
"πρόσβασής σας."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Σε περίπτωση που ξεχάσατε, το όνομα χρήστη σας είναι %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail επαναφοράς συνθηματικού"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Ο κωδικός πρόσβασής σας έχει επαναρυθμιστεί."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Ο κωδικός πρόσβασής σας έχει ρυθμιστεί."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Ρύθμιση κωδικού πρόσβασης"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Λαμβάνετε αυτό το email επειδή εσείς ή κάποιος άλλος προσπάθησε να αποκτήσει "
"πρόσβαση σε έναν λογαριασμό με ηλεκτρονικό ταχυδρομείο %(email)s. Ωστόσο, "
"δεν έχουμε καμία καταγραφή ενός τέτοιου λογαριασμού στη βάση δεδομένων μας."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Αν ήσασταν εσείς, μπορείτε να εγγραφείτε για λογαριασμό χρησιμοποιώντας τον "
"παρακάτω σύνδεσμο."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Άγνωστος λογαριασμός"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Διεύθυνση ηλεκτρονικού ταχυδρομείου"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Τρέχον ηλεκτρονικό ταχυδρομείο"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Αλλάζοντας σε"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr ""
"Η διεύθυνση ηλεκτρονικού ταχυδρομείου σας εκκρεμεί ακόμη προς επαλήθευση."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Ακύρωση Αλλαγή"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Αλλαγή σε"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Αλλαγή ηλεκτρονικού ταχυδρομείου"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Επιβεβαίωση διεύθυνση e-mail"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Παρακαλούμε επιβεβαιώστε ότι το <a href=\"mailto:%(email)s\">%(email)s</a> "
"είναι μια διεύθυνση ηλεκτρονικού ταχυδρομείου για τον χρήστη "
"%(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Δεν είναι δυνατή η επιβεβαίωση του %(email)s επειδή έχει ήδη επιβεβαιωθεί "
"από διαφορετικό λογαριασμό."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Αυτός ο σύνδεσμος επιβεβαίωσης email έληξε ή είναι άκυρος. Παρακαλούμε <a "
"href=\"%(email_url)s\">να εκδώσετε ένα νέο αίτημα επιβεβαίωσης μέσω email</"
"a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Εάν δεν έχετε δημιουργήσει ακόμη λογαριασμό, τότε παρακαλούμε πρώτα να "
"%(link)sεγγραφείτε%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Συνδεθείτε με κλειδί πρόσβασης"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Στείλτε μου έναν κωδικό σύνδεσης"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Αποσύνδεση"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Είστε σίγουροι ότι θέλετε να αποσυνδεθείτε;"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Δεν μπορείτε να αφαιρέσετε την πρωτεύον διεύθυνση email (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "E-mail επιβεβαίωσης στάλθηκε στο %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Έχετε επιβεβαιώσει το %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Αφαιρέθηκε η διεύθυνση e-mail %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Επιτυχημένη σύνδεση ως %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Έχετε αποσυνδεθεί."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Ένας κωδικός εισόδου έχει σταλεί στον %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Αλλαγή συνθηματικού ολοκληρώθηκε επιτυχώς."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Συνθηματικό ορίστηκε επιτυχώς."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Ένας κωδικός εισόδου έχει σταλεί στον %(recipient)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Έχετε επαληθεύσει τον αριθμό τηλεφώνου %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Ορίστηκε η πρωτεύον διεύθυνση e-mail."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Αλλάξτε Συνθηματικό"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Ξεχάσατε τον κωδικό πρόσβασης;"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Ξεχάσατε τον κωδικό πρόσβασής σας; Εισάγετε τη διεύθυνση email σας παρακάτω "
"και θα σας στείλουμε ένα email που θα σας επιτρέψει να τον επαναφέρετε."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Επαναφορά του Συνθηματικού Μου"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Παρακαλούμε επικοινωνήστε μαζί μας αν υπάρξει οποιοδήποτε πρόβλημα κατα την "
"επαναφορά του συνθηματικού σας."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Σας στείλαμε ένα email. Εάν δεν το λάβατε, παρακαλούμε ελέγξτε το φάκελο "
"spam σας. Διαφορετικά, επικοινωνήστε μαζί μας εάν δεν το λάβετε σε λίγα "
"λεπτά."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Μη-έγκυρο Κουπόνι"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Ο σύνδεσμος επαναφορά συνθηματικού δεν ήταν έγκυρος, πιθανών να έχει ήδη "
"χρησιμοποιηθεί.  Παρακαλούμε κάντε εκ νέου <a "
"href=\"%(passwd_reset_url)s\">επαναφορά συνθηματικού</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Το συνθηματικό σας έχει αλλάξει."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Ορισμός Συνθηματικού"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Αλλαγή τηλεφώνου"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Τρέχον τηλέφωνο"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Ο αριθμός τηλεφώνου σας εκκρεμεί ακόμη προς επαλήθευση."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Εισάγετε τον κωδικό πρόσβασής σας:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Θα λάβετε έναν ειδικό κωδικό για είσοδο χωρίς κωδικό πρόσβασης."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Ζητήστε κωδικός"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Άλλες επιλογές σύνδεσης"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Εγγραφή"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Εγγραφή"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Έχετε ήδη λογαριασμό; Τότε παρακαλούμε %(link)sσυνδεθείτε%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Εγγραφείτε χρησιμοποιώντας ένα κλειδί πρόσβασης"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Εγγραφή με κλειδί πρόσβασης"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Άλλες επιλογές"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Φραγή Εγγραφών"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Ζητούμε συγνώμη, αλλά η δυνατότητα εγγραφής είναι υπό φραγή."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Σημείωση"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Έχετε ήδη συνδεθεί ως %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Προσοχη:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Προς το παρόν δεν έχετε δημιουργήσει καμία διεύθυνση ηλεκτρονικού "
"ταχυδρομείου. Θα πρέπει πραγματικά να προσθέσετε μια διεύθυνση ηλεκτρονικού "
"ταχυδρομείου, ώστε να μπορείτε να λαμβάνετε ειδοποιήσεις, να επαναφέρετε τον "
"κωδικό πρόσβασής σας κ.λπ."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Επιβεβαιώστε την διεύθυνση e-mail σας"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Σας στείλαμε ένα email για επαλήθευση. Ακολουθήστε το σύνδεσμο που παρέχεται "
"για να ολοκληρώσετε τη διαδικασία εγγραφής. Εάν δεν βλέπετε το email "
"επαλήθευσης στα κύρια εισερχόμενά σας, ελέγξτε το φάκελο ανεπιθύμητης "
"αλληλογραφίας. Παρακαλούμε επικοινωνήστε μαζί μας εάν δεν λάβετε το email "
"επαλήθευσης μέσα σε λίγα λεπτά."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Αυτό το τμήμα του ιστότοπου απαιτεί να επαληθεύσουμε ότι\n"
"είστε αυτός που ισχυρίζεστε ότι είστε. Για το σκοπό αυτό, απαιτούμε να\n"
"επαληθεύσετε την κυριότητα της διεύθυνσης ηλεκτρονικού ταχυδρομείου σας. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Σας στείλαμε ένα email για\n"
"επαλήθευση. Παρακαλούμε κάντε κλικ στο σύνδεσμο μέσα στο email. Εάν δεν "
"βλέπετε το email επαλήθευσης στα κύρια εισερχόμενά σας, ελέγξτε το φάκελο "
"ανεπιθύμητης αλληλογραφίας. Διαφορετικά\n"
"επικοινωνήστε μαζί μας εάν δεν το λάβετε μέσα σε λίγα λεπτά."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Note:</strong> μπορείτε ακόμη <a href=\"%(email_url)s\">αλλάξτε τη "
"διεύθυνση email σας</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Μηνύματα:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Μενού:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Συνδέσεις Λογαριασμού"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Έλεγχος ταυτότητας δύο παραγόντων"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Συνεδρίες"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Ο λογαριασμός σας προστατεύεται με έλεγχο ταυτότητας δύο παραγόντων. "
"Παρακαλούμε εισάγετε έναν κωδικό ελέγχου ταυτότητας:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Έχει δημιουργηθεί ένα νέο σύνολο κωδικών ανάκτησης για τον έλεγχο ταυτότητας "
"δύο παραγόντων."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Δημιουργία νέων κωδικών ανάκτησης"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Η εφαρμογή Authenticator ενεργοποιήθηκε."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Ενεργοποίηση της εφαρμογής Authenticator"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Η εφαρμογή Authenticator απενεργοποιήθηκε."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Απενεργοποιημένη εφαρμογή Αυθεντικοποιητή"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Έχει προστεθεί ένα νέο κλειδί ασφαλείας."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Προστέθηκε κλειδί ασφαλείας"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Ένα κλειδί ασφαλείας έχει αφαιρεθεί."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Το κλειδί ασφαλείας αφαιρέθηκε"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Εφαρμογή Αυθεντικοποιητή"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr ""
"Ο έλεγχος ταυτότητας με χρήση μιας εφαρμογής ελέγχου ταυτότητας είναι "
"ενεργός."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Μια εφαρμογή ελέγχου ταυτότητας δεν είναι ενεργή."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Απενεργοποίηση"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Ενεργοποίηση"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Κλειδιά ασφαλείας"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Έχετε προσθέσει το κλειδί ασφαλείας %(count)s."
msgstr[1] "Έχετε προσθέσει %(count)s κλειδιά ασφαλείας."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Δεν έχουν προστεθεί κλειδιά ασφαλείας."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Διαχείριση"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Προσθήκη"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Κωδικοί Aνάκτησης"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Υπάρχει %(unused_count)s από τους %(total_count)s διαθέσιμους κωδικούς "
"ανάκτησης."
msgstr[1] ""
"Υπάρχουν %(unused_count)s από τους %(total_count)s διαθέσιμους κωδικούς "
"ανάκτησης."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Δεν έχουν οριστεί κωδικοί ανάκτησης."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Προβολή"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Λήψη"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Παραγάγετε"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Δημιουργήθηκε ένα νέο σύνολο κωδικών ανάκτησης."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Προστέθηκε κλειδί ασφαλείας."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Το κλειδί ασφαλείας αφαιρέθηκε."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Εισάγετε έναν κωδικό ελέγχου ταυτότητας:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Πρόκειται να δημιουργήσετε ένα νέο σύνολο κωδικών ανάκτησης για το "
"λογαριασμό σας."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Αυτή η ενέργεια θα ακυρώσει τους υπάρχοντες κωδικούς σας."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Είσαι σίγουρος;"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Αχρησιμοποίητοι κωδικοί"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Λήψη κωδικών"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Δημιουργία νέων κωδικών"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Ενεργοποιήστε την εφαρμογή Authenticator"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Για να προστατέψετε το λογαριασμό σας με έλεγχο ταυτότητας δύο παραγόντων, "
"σαρώστε τον παρακάτω κωδικό QR με την εφαρμογή ελέγχου ταυτότητας. Στη "
"συνέχεια, εισαγάγετε τον κωδικό επαλήθευσης που δημιουργείται από την "
"παρακάτω εφαρμογή."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Μυστικό αυθεντικοποιητή"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Μπορείτε να αποθηκεύσετε αυτό το μυστικό και να το χρησιμοποιήσετε για να "
"επανεγκαταστήσετε την εφαρμογή ελέγχου ταυτότητας σε μεταγενέστερο χρόνο."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Απενεργοποίηση της εφαρμογής Authenticator"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Πρόκειται να απενεργοποιήσετε τον έλεγχο ταυτότητας με βάση την εφαρμογή "
"authenticator. Είστε σίγουροι;"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "Εμπιστεύεστε αυτό το πρόγραμμα περιήγησης;"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""
"Εάν επιλέξετε να εμπιστευτείτε αυτό το πρόγραμμα περιήγησης, δεν θα σας "
"ζητηθεί κωδικός επαλήθευσης την επόμενη φορά που θα συνδεθείτε."

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "Εμπιστοσύνη για %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "Μην εμπιστεύεστε"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Προσθήκη κλειδιού ασφαλείας"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Αφαίρεση κλειδιού ασφαλείας"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Είστε σίγουροι ότι θέλετε να αφαιρέσετε αυτό το κλειδί ασφαλείας;"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Χρήση"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Κλειδί πρόσβασης"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Κλειδί ασφαλείας"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Αυτό το κλειδί δεν υποδεικνύει αν είναι κλειδί πρόσβασης."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Απροσδιόριστο"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Προστέθηκε στις %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Τελευταία χρήση %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Επεξεργασία"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Επεξεργασία κλειδιού ασφαλείας"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Αποθήκευση"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Δημιουργία κλειδιού πρόσβασης"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Πρόκειται να δημιουργήσετε ένα κλειδί πρόσβασης για το λογαριασμό σας. Καθώς "
"μπορείτε να προσθέσετε επιπλέον κλειδιά αργότερα, μπορείτε να "
"χρησιμοποιήσετε ένα περιγραφικό όνομα για να ξεχωρίζετε τα κλειδιά."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Δημιουργία"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Αυτή η λειτουργία απαιτεί JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Αποτυχία σύνδεσης τρίτου μέρους"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Παρουσιάστηκε σφάλμα κατά την προσπάθεια σύνδεσης μέσω του λογαριασμού σας "
"τρίτου μέρους."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Μπορείτε να συνδεθείτε στο λογαριασμό σας χρησιμοποιώντας οποιονδήποτε από "
"τους ακόλουθους λογαριασμούς τρίτων:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr ""
"Προς το παρόν δεν έχετε συνδέσει λογαριασμούς τρίτων με αυτόν το λογαριασμό."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Προσθήκη λογαριασμού τρίτου μέρους"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"Ένας λογαριασμός τρίτου μέρους από τον %(provider)s έχει συνδεθεί με το "
"λογαριασμό σας."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Συνδεδεμένος λογαριασμός τρίτου μέρους"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"Ένας λογαριασμός τρίτου μέρους από τον %(provider)s έχει αποσυνδεθεί από το "
"λογαριασμό σας."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Αποσυνδεδεμένος λογαριασμός τρίτου μέρους"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Συνδέστε %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Πρόκειται να συνδέσετε έναν νέο λογαριασμό τρίτου μέρους από τον "
"%(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Συνδεθείτε με %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Πρόκειται να συνδεθείτε χρησιμοποιώντας έναν λογαριασμό τρίτου μέρους από "
"τον %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Συνέχεια"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Σύνδεση ακυρώθηκε"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Αποφασίσατε να ακυρώσετε την σύνδεση σας στην ιστοσελίδα με έναν από τους "
"υπάρχοντες λογαριασμούς σας. Αν έγινε κατά λάθος, παρακαλώ <a "
"href=\"%(login_url)s\">συνδεθείτε</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Ο λογαριασμός τρίτου μέρους έχει συνδεθεί."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Ο λογαριασμός τρίτου μέρους έχει αποσυνδεθεί."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Πρόκειται να χρησιμοποιήσετε τον %(provider_name)s λογαριασμό σας για να "
"συνδεθείτε στην σελίδα\n"
"%(site_name)s. Ως τελικό βήμα, παρακαλούμε συμπληρώστε την παρακάτω φόρμα:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Ή χρησιμοποιήστε ένα τρίτο μέρος"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Αποχώρησε από όλες τις άλλες συνεδρίες."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Ξεκίνησε στο"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "Διεύθυνση IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Πρόγραμμα περιήγησης"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Τελευταία φορά στο"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Τρέχον"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Αποχώρηση από άλλες συνεδρίες"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Συνεδρίες χρηστών"

#: usersessions/models.py:92
msgid "session key"
msgstr "κλειδί συνεδρίας"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Συνδέσεις Λογαριασμού"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Το συνθηματικό πρέπει να περιέχει τουλάχιστον {0} χαρακτήρες."

#, fuzzy, python-format
#~| msgid ""
#~| "Hello from %(site_name)s!\n"
#~| "\n"
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Χαιρετίσματα από το %(site_name)s!\n"
#~ "\n"
#~ "Λαμβάνετε αυτό το e-mail επειδή εσείς ή κάποιος άλλος έχει κάνει αίτηση "
#~ "συνθηματικού για τον λογαριασμό σας.\n"
#~ "Αν δεν ζητήσατε επαναφορά συνθηματικού, μπορεί να αγνοηθεί με ασφάλεια. "
#~ "Πατήστε στον σύνδεσμο που ακολουθεί για να επαναφέρετε το συνθηματικό σας."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr ""
#~ "Οι διευθύνσεις e-mail που ακολουθούν είναι συσχετισμένες με τον "
#~ "λογαριασμό σας:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Επιβεβαίωση διεύθυνση e-mail"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Παρακαλούμε συνδεθείτε με έναν\n"
#~ "από τους ήδη υπάρχοντες εξωτερικούς λογαριασμούς σας. Ή, <a "
#~ "href=\"%(signup_url)s\">κάντε εγγραφή</a>\n"
#~ "για έναν λογαριασμό %(site_name)s και συνδεθείτε παρακάτω:"

#~ msgid "or"
#~ msgstr "ή"

#~ msgid "change password"
#~ msgstr "αλλαγή συνθηματικού"

#~ msgid "OpenID Sign In"
#~ msgstr "Σύνδεση OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Αυτό το e-mail χρησιμοποιείται ήδη από άλλο λογαριασμό."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Σας έχουμε στείλει ένα e-mail. Παρακαλούμε επικοινωνήστε μαζί μας αν δεν "
#~ "το έχετε παραλάβει μέσα σε λίγα λεπτά."
