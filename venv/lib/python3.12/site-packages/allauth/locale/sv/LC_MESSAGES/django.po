# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-03-29 15:01+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Swedish <https://hosted.weblate.org/projects/allauth/django-"
"allauth/sv/>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Detta konto är inaktivt."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Du kan inte ta bort din primära epost-adress."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Denna epost-adress är redan knuten till detta konto"

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Epost-adressen och/eller lösenordet är felaktigt."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Telefonnumret och/eller lösenordet är felaktigt."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "En användare är redan registrerad med den här epost-adressen"

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Skriv in ditt nuvarande lösenord."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Felaktig kod."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Felaktigt lösenord."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Felaktig  eller utgången nyckel."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Felaktig inlogging."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr ""

#: account/adapter.py:75
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Ditt konto har ingen verifierad epost-adress."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "En användare är redan registrerad med den här epost-adressen"

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr ""

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "Epost-adressen är inte knuten till något konto"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "Epost-adressen är inte knuten till något konto"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Din primära epost-adress måste verifieras."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Användarnamnet kan ej användas. Välj ett annat användarnamn."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Användarnamnet och/eller lösenordet är felaktigt."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
#, fuzzy
#| msgid "Forgot Password?"
msgid "Use your password"
msgstr "Glömt lösenordet?"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr ""

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr ""

#: account/admin.py:23
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Mark selected email addresses as verified"
msgstr "Din primära epost-adress måste verifieras."

#: account/apps.py:11
#, fuzzy
msgid "Accounts"
msgstr "Konto"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Du måste ange samma lösenord"

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Lösenord"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Kom ihåg mig"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Epost-adress"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "Epost"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Användarnamn"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Logga in"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Användarnamn eller epost-adress"

#: account/forms.py:156
msgid "Username or email"
msgstr "Användarnamn eller epost-adress"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Användarnamn eller epost-adress"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "Epost (valfritt)"

#: account/forms.py:183
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "Glömt lösenordet?"

#: account/forms.py:334
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "Epost (valfritt)"

#: account/forms.py:339
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "epost-bekräftelse"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "Epost (valfritt)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "Epost (valfritt)"

#: account/forms.py:435
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "Du måste ange samma lösenord"

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Lösenord (igen)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Nuvarande lösenord"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nytt lösenord"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nytt lösenord (igen)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr ""

#: account/models.py:26
msgid "user"
msgstr ""

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "epost-adress"

#: account/models.py:34
#, fuzzy
msgid "verified"
msgstr "Ej verifierad"

#: account/models.py:35
#, fuzzy
msgid "primary"
msgstr "Primär"

#: account/models.py:41
msgid "email addresses"
msgstr "epost-adresser"

#: account/models.py:151
msgid "created"
msgstr ""

#: account/models.py:152
msgid "sent"
msgstr ""

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr ""

#: account/models.py:158
msgid "email confirmation"
msgstr "epost-bekräftelse"

#: account/models.py:159
msgid "email confirmations"
msgstr "epost-bekräftelser"

#: headless/apps.py:7
msgid "Headless"
msgstr ""

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr ""

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:141
msgid "Master key"
msgstr ""

#: mfa/adapter.py:143
msgid "Backup key"
msgstr ""

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr ""

#: mfa/apps.py:9
msgid "MFA"
msgstr ""

#: mfa/models.py:24
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr ""

#: mfa/models.py:26
msgid "WebAuthn"
msgstr ""

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr ""

#: mfa/webauthn/forms.py:59
#, fuzzy
#| msgid "Password"
msgid "Passwordless"
msgstr "Lösenord"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""

#: socialaccount/adapter.py:39
#, fuzzy
#| msgid "Bad Token"
msgid "Invalid token."
msgstr "Felaktig nyckel"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Ditt konto har inget lösenord."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Ditt konto har ingen verifierad epost-adress."

#: socialaccount/adapter.py:43
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Du kan logga in på ditt konto via något av följande tredjeparts-konton:"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "Tredjeparts-kontot är redan knutet till ett annat konto."

#: socialaccount/apps.py:9
#, fuzzy
msgid "Social Accounts"
msgstr "Konto"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr ""

#: socialaccount/models.py:52
msgid "provider ID"
msgstr ""

#: socialaccount/models.py:56
#, fuzzy
msgid "name"
msgstr "Användarnamn"

#: socialaccount/models.py:58
msgid "client id"
msgstr ""

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr ""

#: socialaccount/models.py:63
msgid "secret key"
msgstr ""

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr ""

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr ""

#: socialaccount/models.py:81
msgid "social application"
msgstr ""

#: socialaccount/models.py:82
msgid "social applications"
msgstr ""

#: socialaccount/models.py:117
msgid "uid"
msgstr ""

#: socialaccount/models.py:119
msgid "last login"
msgstr ""

#: socialaccount/models.py:120
msgid "date joined"
msgstr ""

#: socialaccount/models.py:121
msgid "extra data"
msgstr ""

#: socialaccount/models.py:125
msgid "social account"
msgstr ""

#: socialaccount/models.py:126
msgid "social accounts"
msgstr ""

#: socialaccount/models.py:160
msgid "token"
msgstr ""

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""

#: socialaccount/models.py:165
msgid "token secret"
msgstr ""

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""

#: socialaccount/models.py:169
msgid "expires at"
msgstr ""

#: socialaccount/models.py:174
msgid "social application token"
msgstr ""

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr ""

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
#, fuzzy
#| msgctxt "field label"
#| msgid "Login"
msgid "Login"
msgstr "Logga in"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Felaktigt svar vid hämtning av fråge-nyckel från \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Felaktigt svar vid hämtning av access-nyckel från \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Ingen fråge-nyckel sparad för \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Ingen access-nyckel sparad för \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Ingen access till privata resurser hos \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Felaktigt svar vid hämtning av fråge-nyckel från \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Kontot inaktivt"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Detta konto är inaktivt."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Verifiera"

#: templates/account/base_confirm_code.html:43
msgid "Request new code"
msgstr ""

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Verifiera epost-adress"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr ""

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr ""

#: templates/account/confirm_email_verification_code.html:5
#, fuzzy
#| msgid "email confirmation"
msgid "Email Verification"
msgstr "epost-bekräftelse"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr ""

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "epost-adress"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Logga in"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr ""

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Återställning av lösenord"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Återställning av lösenord"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Sänd verifiering igen"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Re-send Verification"
msgid "Enter Phone Verification Code"
msgstr "Sänd verifiering igen"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Epost-adresser"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Följande epost-adresser är knutna till ditt konto:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Verifierad"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Ej verifierad"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Primär"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Gör primär"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Sänd verifiering igen"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Ta bort"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Lägg till epost-adress"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Lägg till epost"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Vill du verkligen ta bort den valda epost-adressen?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr ""

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Email address"
msgid "Email Changed"
msgstr "Epost-adress"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "Your password has been deleted."
msgid "Your email has been confirmed."
msgstr "Ditt lösenord har tagits bort."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "epost-bekräftelse"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "User %(user_display)s at %(site_name)s has given this as an email "
#| "address.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Användare %(user_display)s på %(site_name)s har angivit detta som sin epost-"
"adress.\n"
"\n"
"Klicka på följande adress för att bekräfta att detta stämmer: "
"%(activate_url)s\n"

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""

#: templates/account/email/email_confirmation_subject.txt:3
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Please Confirm Your Email Address"
msgstr "Verifiera epost-adress"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "Ta bort"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""

#: templates/account/email/login_code_subject.txt:3
#, fuzzy
#| msgid "Sign In"
msgid "Sign-In Code"
msgstr "Logga in"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "Ditt lösenord är nu ändrat."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "Lösenord (igen)"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Återställning av lösenord"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Du har fått detta mail för att du eller någon annan har begärt en "
"återställning av ditt lösenord på %(site_domain)s.\n"
"Du kan bortse från detta mail om du inte begärt en återställning. Klicka på "
"länken nedan för att återställa ditt lösenord."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Ditt användarnamn är %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Återställning av lösenord"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password has been deleted."
msgid "Your password has been reset."
msgstr "Ditt lösenord har tagits bort."

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password has been deleted."
msgid "Your password has been set."
msgstr "Ditt lösenord har tagits bort."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "Återställning av lösenord"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""

#: templates/account/email/unknown_account_subject.txt:3
#, fuzzy
#| msgid "Account"
msgid "Unknown Account"
msgstr "Konto"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "Epost-adresser"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
#, fuzzy
#| msgid "Current Password"
msgid "Current email"
msgstr "Nuvarande lösenord"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr ""

#: templates/account/email_change.html:35
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification."
msgstr "Din primära epost-adress måste verifieras."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr ""

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
#, fuzzy
#| msgid "Email"
msgid "Change to"
msgstr "Epost"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "Epost"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Verifiera epost-adress"

#: templates/account/email_confirm.html:16
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Verifiera att <a href=\"mailto:%(email)s\">%(email)s</a> är en epost-adress "
"för %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Tredjeparts-kontot är redan knutet till ett annat konto."

#: templates/account/email_confirm.html:36
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a "
#| "href=\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Länken för att verifiera epost-adressen har förfallit eller är ogiltig. <a "
"href=\"%(email_url)s\">Skapa en ny epost-verification.</a>"

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "Har du redan ett konto? Då kan du %(link)slogga in%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr ""

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr ""

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Logga ut"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Är du säker att du vill logga ut?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Du kan inte ta bort din primära epost-adress (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Verifierings-mail skickat till %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Du har verifierat %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Ta bort epost-adress %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Du har loggat in som %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Du har loggat ut."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr ""

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Lösenordet ändrat."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Lösenord skapat."

#: templates/account/messages/phone_verification_sent.txt:2
#, python-format
msgid "A verification code has been sent to %(phone)s."
msgstr ""

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primär epost-adress satt."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Ändra lösenord"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Glömt lösenordet?"

#: templates/account/password_reset.html:14
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Glömt ditt lösenord? Ange din epost-adress nedan så skickar vi ett mail med "
"instruktioner för att återställa det."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Återställ mitt lösenord"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Vänligen kontakta oss om du har problem med att återställa ditt lösenord."

#: templates/account/password_reset_done.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Vi har skickat ett mail till dig för\n"
"verifiering. Klicka på länken inne i detta mail. Kontakta\n"
"oss om det inte dyker upp inom ett par minuter."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Felaktig nyckel"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Länken för att återställa lösenordet var ogiltig, möjligtvis för att den "
"redan har använts. Begär en <a href=\"%(passwd_reset_url)s\">ny lösenords-"
"återställning</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Ditt lösenord är nu ändrat."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Skapa lösenord"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Email"
msgid "Change Phone"
msgstr "Epost"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current Password"
msgid "Current phone"
msgstr "Nuvarande lösenord"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your phone number is still pending verification."
msgstr "Din primära epost-adress måste verifieras."

#: templates/account/reauthenticate.html:6
#, fuzzy
#| msgid "Forgot Password?"
msgid "Enter your password:"
msgstr "Glömt lösenordet?"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr ""

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr ""

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr ""

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Skapa konto"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Skapa konto"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Har du redan ett konto? Då kan du %(link)slogga in%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr ""

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "Skapa konto"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr ""

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Anmälan stängd"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Tyvärr är anmälan stängd för närvarande."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Information"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "du är inloggad som %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Varning:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Du har inte angett någon epost-adress. Du borde lägga till en epost-adress "
"så du kan få meddelanden, återställa ditt lösenord och liknande."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Verifiera din epost-adress"

#: templates/account/verification_sent.html:12
#, fuzzy
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Ett mail för verifiering har skickats till <a href=\"mailto:"
"%(email)s\">%(email)s</a>. Klicka på länken i mailet för att slutföra "
"anmälan. Kontakta oss om det inte dyker upp inom ett par minuter."

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"För att få tillgång till denna avdelning måste du verifiera att\n"
"du är den du säger att du är. Du måste därför bevisa att du\n"
"angett rätt epost-adress. "

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Vi har skickat ett mail till dig för\n"
"verifiering. Klicka på länken inne i detta mail. Kontakta\n"
"oss om det inte dyker upp inom ett par minuter."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Information:</strong> du kan fortfarande <a "
"href=\"%(email_url)s\">ändra din epost-adress</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Anslutna tredjeparts-konton"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr ""

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr ""

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr ""

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr ""

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr ""

#: templates/mfa/email/webauthn_removed_message.txt:4
#, fuzzy
#| msgid "Your password has been deleted."
msgid "A security key has been removed."
msgstr "Ditt lösenord har tagits bort."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr ""

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr ""

#: templates/mfa/index.html:62
msgid "Manage"
msgstr ""

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr ""

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:96
msgid "View"
msgstr ""

#: templates/mfa/index.html:102
msgid "Download"
msgstr ""

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr ""

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr ""

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr ""

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr ""

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr ""

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
#, fuzzy
#| msgid "Are you sure you want to sign out?"
msgid "Are you sure you want to remove this security key?"
msgstr "Är du säker att du vill logga ut?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:41
#, fuzzy
#| msgid "Unverified"
msgid "Unspecified"
msgstr "Ej verifierad"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr ""

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Current Password"
msgid "Create Passkey"
msgstr "Nuvarande lösenord"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr ""

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "Fel vid tredjeparts-inloggning"

#: templates/socialaccount/authentication_error.html:12
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Ett fel inträffade vid inloggning via tredjeparts-konto."

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Du kan logga in på ditt konto via något av följande tredjeparts-konton:"

#: templates/socialaccount/connections.html:46
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "Du har för närvarande inga tredjeparts-konton knutna till ditt konto."

#: templates/socialaccount/connections.html:50
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "Lägg till tredjeparts-konto"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Connected"
msgstr "Lägg till tredjeparts-konto"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Disconnected"
msgstr "Lägg till tredjeparts-konto"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Inloggning avbruten"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Du valde att avbryta inloggningen via ett av dina tredjeparts-konton. Om "
"detta var ett misstag kan du <a href=\"%(login_url)s\">logga in igen</a>."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "Tredjeparts-kontot har knutits till ditt kontot."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "Tredjeparts-kontot har tagits bort."

#: templates/socialaccount/signup.html:12
#, fuzzy, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Du håller på att logga in via ditt konto på %(provider_name)s på \n"
"%(site_name)s. Fyll i följande formulär för att slutföra inloggningen:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr ""

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Addresses"
msgid "IP Address"
msgstr "Epost-adresser"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
#, fuzzy
#| msgid "Current Password"
msgid "Current"
msgstr "Nuvarande lösenord"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:92
msgid "session key"
msgstr ""

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Anslutna tredjeparts-konton"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Lösenordet måste vara minst {0} tecken långt"

#, fuzzy, python-format
#~| msgid ""
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account at %(site_domain)s.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Du har fått detta mail för att du eller någon annan har begärt en "
#~ "återställning av ditt lösenord på %(site_domain)s.\n"
#~ "Du kan bortse från detta mail om du inte begärt en återställning. Klicka "
#~ "på länken nedan för att återställa ditt lösenord."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Följande epost-adresser är knutna till ditt konto:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Verifiera epost-adress"

#, fuzzy, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Logga in med ett\n"
#~ "av dina befintliga tredjeparts-konton eller <a "
#~ "href=\"%(signup_url)s\">skapa ett konto</a> \n"
#~ "för %(site_name)s och logga in nedan:"

#~ msgid "or"
#~ msgstr "eller"

#~ msgid "change password"
#~ msgstr "ändra lösenord"

#~ msgid "OpenID Sign In"
#~ msgstr "Inloggning via OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Denna epost-adress är redan knuten till ett annat konto"

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Ett mail har nu skickats. Kontakta oss om det inte dyker upp inom ett par "
#~ "minuter."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Inloggningen och/eller lösenordet är felaktigt."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Användarnamn kan endast innehålla bokstäver, siffror samt @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Användarnamnet är upptaget. Välj ett annat."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Logga in"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Du har verifierat att <a href=\"mailto:%(email)s\">%(email)s</a> är en "
#~ "epost-adress för %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Tack för att du använder vår hemsida!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Epost-bekräftelse skickad till %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Ta bort lösenordet"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "Du kan ta bort ditt lösenord eftersom du är inloggad via OpenID."

#~ msgid "delete my password"
#~ msgstr "ta bort mitt lösenord"

#~ msgid "Password Deleted"
#~ msgstr "Lösenordet borttaget"
