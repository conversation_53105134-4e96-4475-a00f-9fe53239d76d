from django.db import models
from django.core.validators import MinValueValidator
from accounts.models import Branch


class Category(models.Model):
    """Product categories"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='categories/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Categories"


class Subcategory(models.Model):
    """Product subcategories"""

    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='subcategories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.category.name} - {self.name}"

    class Meta:
        verbose_name_plural = "Subcategories"
        unique_together = ['category', 'name']


class Brand(models.Model):
    """Product brands"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    logo = models.ImageField(upload_to='brands/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class Product(models.Model):
    """Product model with pricing for different user types"""

    title = models.CharField(max_length=200)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    subcategory = models.ForeignKey(Subcategory, on_delete=models.CASCADE, related_name='products', null=True, blank=True)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, related_name='products', null=True, blank=True)

    # Pricing
    normal_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    reseller_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])

    # Images
    main_image = models.ImageField(upload_to='products/')

    # Product details
    sku = models.CharField(max_length=50, unique=True)
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    dimensions = models.CharField(max_length=100, blank=True)  # e.g., "10x5x2 cm"

    # Status
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)

    # SEO
    meta_title = models.CharField(max_length=200, blank=True)
    meta_description = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    def get_price_for_user(self, user):
        """Get price based on user type"""
        if hasattr(user, 'is_reseller') and user.is_reseller:
            return self.reseller_price
        return self.normal_price

    def get_main_image_url(self):
        if self.main_image:
            return self.main_image.url
        return '/static/images/no-image.png'

    class Meta:
        ordering = ['-created_at']


class ProductImage(models.Model):
    """Additional product images"""

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/')
    alt_text = models.CharField(max_length=200, blank=True)
    is_primary = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Image for {self.product.title}"


class Stock(models.Model):
    """Stock management for each branch"""

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='stock_items')
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='stock_items')
    quantity = models.PositiveIntegerField(default=0)
    reserved_quantity = models.PositiveIntegerField(default=0)  # For pending orders
    minimum_stock = models.PositiveIntegerField(default=10)
    maximum_stock = models.PositiveIntegerField(default=1000)

    # Stock tracking
    last_restocked = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.product.title} - {self.branch.name}: {self.quantity}"

    @property
    def available_quantity(self):
        """Available quantity excluding reserved items"""
        return max(0, self.quantity - self.reserved_quantity)

    @property
    def is_low_stock(self):
        """Check if stock is below minimum threshold"""
        return self.available_quantity <= self.minimum_stock

    @property
    def is_out_of_stock(self):
        """Check if product is out of stock"""
        return self.available_quantity == 0

    class Meta:
        unique_together = ['product', 'branch']
