from django.db import models
from django.contrib.auth import get_user_model
from accounts.models import Branch
from inventory.models import Product, Category, Brand
from orders.models import Order

User = get_user_model()


class SalesReport(models.Model):
    """Sales reports for different time periods"""

    REPORT_TYPES = (
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    )

    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='sales_reports', null=True, blank=True)

    # Date range
    start_date = models.DateField()
    end_date = models.DateField()

    # Sales metrics
    total_orders = models.PositiveIntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_items_sold = models.PositiveIntegerField(default=0)
    average_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Customer metrics
    new_customers = models.PositiveIntegerField(default=0)
    returning_customers = models.PositiveIntegerField(default=0)

    # Product metrics
    top_selling_product = models.ForeignKey(Product, on_delete=models.SET_NULL, null=True, blank=True)
    top_selling_category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        branch_name = self.branch.name if self.branch else "All Branches"
        return f"{self.report_type.title()} Report - {branch_name} ({self.start_date} to {self.end_date})"

    class Meta:
        unique_together = ['report_type', 'branch', 'start_date', 'end_date']
        ordering = ['-start_date']


class ProductAnalytics(models.Model):
    """Product performance analytics"""

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='analytics')
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='product_analytics', null=True, blank=True)

    # Date range
    date = models.DateField()

    # Sales metrics
    units_sold = models.PositiveIntegerField(default=0)
    revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    profit = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Inventory metrics
    stock_level = models.PositiveIntegerField(default=0)
    stock_turnover = models.DecimalField(max_digits=8, decimal_places=2, default=0)

    # Customer metrics
    unique_customers = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        branch_name = self.branch.name if self.branch else "All Branches"
        return f"{self.product.title} - {branch_name} ({self.date})"

    class Meta:
        unique_together = ['product', 'branch', 'date']
        ordering = ['-date']


class CustomerAnalytics(models.Model):
    """Customer behavior analytics"""

    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='analytics')

    # Date range
    date = models.DateField()

    # Order metrics
    total_orders = models.PositiveIntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    average_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Behavior metrics
    days_since_last_order = models.PositiveIntegerField(default=0)
    favorite_category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    favorite_brand = models.ForeignKey(Brand, on_delete=models.SET_NULL, null=True, blank=True)

    # Engagement
    login_count = models.PositiveIntegerField(default=0)
    page_views = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.customer.get_full_name()} - {self.date}"

    class Meta:
        unique_together = ['customer', 'date']
        ordering = ['-date']


class InventoryAnalytics(models.Model):
    """Inventory performance analytics"""

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory_analytics')
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='inventory_analytics')

    # Date
    date = models.DateField()

    # Stock metrics
    opening_stock = models.PositiveIntegerField(default=0)
    closing_stock = models.PositiveIntegerField(default=0)
    stock_received = models.PositiveIntegerField(default=0)
    stock_sold = models.PositiveIntegerField(default=0)
    stock_adjusted = models.IntegerField(default=0)  # Can be negative

    # Performance metrics
    stock_turnover_rate = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    days_of_stock = models.PositiveIntegerField(default=0)

    # Alerts
    is_low_stock = models.BooleanField(default=False)
    is_out_of_stock = models.BooleanField(default=False)
    is_overstock = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.title} - {self.branch.name} ({self.date})"

    class Meta:
        unique_together = ['product', 'branch', 'date']
        ordering = ['-date']


class SystemMetrics(models.Model):
    """Overall system performance metrics"""

    date = models.DateField(unique=True)

    # User metrics
    total_users = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    new_registrations = models.PositiveIntegerField(default=0)

    # Order metrics
    total_orders = models.PositiveIntegerField(default=0)
    completed_orders = models.PositiveIntegerField(default=0)
    cancelled_orders = models.PositiveIntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Product metrics
    total_products = models.PositiveIntegerField(default=0)
    active_products = models.PositiveIntegerField(default=0)
    low_stock_products = models.PositiveIntegerField(default=0)
    out_of_stock_products = models.PositiveIntegerField(default=0)

    # Performance metrics
    average_response_time = models.DecimalField(max_digits=8, decimal_places=2, default=0)  # in seconds
    error_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # percentage

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"System Metrics - {self.date}"

    class Meta:
        ordering = ['-date']
