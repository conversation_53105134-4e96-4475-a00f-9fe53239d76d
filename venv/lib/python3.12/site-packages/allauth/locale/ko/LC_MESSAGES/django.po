# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-11-03 04:02+0000\n"
"Last-Translator: 황인아 <<EMAIL>>\n"
"Language-Team: Korean <https://hosted.weblate.org/projects/allauth/django-"
"allauth/ko/>\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.8.2\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "해당 계정은 현재 비활성화 상태입니다."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "주 이메일은 제거할 수 없습니다."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "해당 이메일은 이미 이 계정에 등록되어 있습니다."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "이메일 또는 비밀번호가 올바르지 않습니다."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "아이디 또는 비밀번호가 올바르지 않습니다."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "해당 이메일은 이미 사용되고 있습니다."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "현재 비밀번호를 입력하세요."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "올바르지 않은 코드."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "올바르지 않은 비밀번호."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "올비르지 않거나 만료된 키."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "유효하지 않은 토큰."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "비밀번호 초기화 토큰이 올바르지 않습니다."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "이메일 주소는 %d개 이상 추가할 수 없습니다."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "해당 이메일은 이미 사용되고 있습니다."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "너무 많은 로그인 실패가 감지되었습니다. 잠시 후에 다시 시도하세요."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "해당 이메일을 가지고 있는 사용자가 없습니다."

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "해당 이메일을 가지고 있는 사용자가 없습니다."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "주 이메일은 인증이 필요합니다."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "해당 아이디는 이미 사용중입니다. 다른 사용자명을 이용해 주세요."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "아이디 또는 비밀번호가 올바르지 않습니다."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "비밀번호를 사용하세요"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "인증 앱 또는 코드를 사용하세요"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "보안키를 사용하세요"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "선택된 이메일 주소를 인증됨으로 표시"

#: account/apps.py:11
msgid "Accounts"
msgstr "계정"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "동일한 비밀번호를 입력해야 합니다."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "비밀번호"

#: account/forms.py:100
msgid "Remember Me"
msgstr "아이디 저장"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "이메일 주소"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "이메일"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "아이디"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "로그인"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "아이디 또는 이메일"

#: account/forms.py:156
msgid "Username or email"
msgstr "아이디 또는 이메일"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "아이디 또는 이메일"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "이메일 (선택사항)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "비밀번호를 잊으셨나요?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "이메일 (확인)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "이메일 주소 확인"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "이메일 (선택사항)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "이메일 (선택사항)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "동일한 이메일을 입력해야 합니다."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "비밀번호 (확인)"

#: account/forms.py:645
msgid "Current Password"
msgstr "현재 비밀번호"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "새 비밀번호"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "새 비밀번호 (확인)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "코드"

#: account/models.py:26
msgid "user"
msgstr "사용자"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "이메일 주소"

#: account/models.py:34
msgid "verified"
msgstr "인증완료"

#: account/models.py:35
msgid "primary"
msgstr "주"

#: account/models.py:41
msgid "email addresses"
msgstr "이메일 주소"

#: account/models.py:151
msgid "created"
msgstr "생성됨"

#: account/models.py:152
msgid "sent"
msgstr "전송됨"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "키"

#: account/models.py:158
msgid "email confirmation"
msgstr "이메일 확인"

#: account/models.py:159
msgid "email confirmations"
msgstr "이메일 확인"

#: headless/apps.py:7
msgid "Headless"
msgstr "헤드리스"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr "2단계 인증으로 보호되는 계정에 이메일 주소를 추가할 수 없습니다."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "2단계 인증을 비활성화 할 수 없습니다."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr "2단계 인증을 활성화하지 않으면 복구 코드를 생성할 수 없습니다."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr "이메일 주소를 인증하기 전까지는 2단계 인증을 활성화할 수 없습니다."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "마스터 키"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "벡업 키"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "키 번호. {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "복구 코드"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP 인증"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "웹인증"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "인증 코드"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "비밀번호 없음"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"비밀번호 없는 작업을 활성화하면 이 키만 사용하여 로그인할 수 있지만, 생체 인"
"식이나 PIN 보호와 같은 추가 요구 사항이 적용됩니다."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"해당 이메일을 사용중인 계정이 이미 존재합니다. 해당 계정으로 로그인 후에 %s "
"계정으로 연결하세요."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "유효하지 않은 토큰."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "당신의 계정에 비밀번호가 설정되어있지 않습니다."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "당신의 계정에는 인증된 이메일이 없습니다."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "마지막 남은 소셜 계정은 연결해제 할수 없습니다."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "해당 소셜 계정이 이미 다른 계정에 연결되어 있습니다."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "소셜 계정"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "제공자"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "제공자 ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "이름"

#: socialaccount/models.py:58
msgid "client id"
msgstr "클라이언트 아이디"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "앱 아이디 또는 컨슈머 아이디"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "비밀 키"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API 비밀 키, 클라이언트 비밀 키, 또는 컨슈머 비밀 키"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "키"

#: socialaccount/models.py:81
msgid "social application"
msgstr "소셜 어플리케이션"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "소셜 어플리케이션"

#: socialaccount/models.py:117
msgid "uid"
msgstr "사용자 식별"

#: socialaccount/models.py:119
msgid "last login"
msgstr "최종 로그인"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "가입 날짜"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "추가 정보"

#: socialaccount/models.py:125
msgid "social account"
msgstr "소셜 계정"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "소셜 계정"

#: socialaccount/models.py:160
msgid "token"
msgstr "토큰"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) 또는 access token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "시크릿 토큰"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) 또는 refresh token (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "만료일"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "소셜 어플리케이션 토큰"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "소셜 어플리케이션 토큰"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "올바르지 않은 프로필 데이터"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "로그인"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "취소"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"\"%s\".로 부터 요청 토큰을 받는 도중 잘못된 응답을 받았습니다. 응답은 %s였습"
"니다."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "\"%s\".로 부터 access 토큰을 받는 도중 잘못된 응답을 받았습니다."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "\"%s\".을(를) 위한 request 토큰이 없습니다."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "\"%s\".을(를) 위한 access 토큰이 없습니다."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "\"%s\".에 접근하기 위한 권한이 없습니다."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "\"%s\".로 부터 request 토큰을 받는 도중 잘못된 응답을 받았습니다."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "계정 비활성"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "해당 계정은 비활성화된 상태입니다."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"%(email_link)s로 코드를 보냈습니다. 코드가 곧만료되므로 즉시 입력하세요."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "확인"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "코드 요청"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "접근 확인"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "계정을 보호하려면 다시 인증하세요."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "대체 옵션"

#: templates/account/confirm_email_verification_code.html:5
#, fuzzy
#| msgid "Email Confirmation"
msgid "Email Verification"
msgstr "이메일 확인"

#: templates/account/confirm_email_verification_code.html:8
#, fuzzy
#| msgid "Enter an authenticator code:"
msgid "Enter Email Verification Code"
msgstr "인증 코드 입력:"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "이메일 주소"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "로그인"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "로그인 코드 입력"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "비밀번호 초기화"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "비밀번호 초기화"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "인증 재전송"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter an authenticator code:"
msgid "Enter Phone Verification Code"
msgstr "인증 코드 입력:"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "이메일 계정"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "다음 이메일 주소들이 당신의 계정에 등록되어 있습니다."

#: templates/account/email.html:25
msgid "Verified"
msgstr "인증완료"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "인증대기"

#: templates/account/email.html:34
msgid "Primary"
msgstr "주"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "주 이메일로 지정"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "인증 재전송"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "제거"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "이메일 주소 추가"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "이메일 추가"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "정말로 선택하신 이메일을 제거하시겠습니까?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"귀하 또는 다른 사람이 다음 이메일 주소를 사용하여 계정을 등록하려고 시도했기"
"에 이 이메일을 받으셨습니다:\n"
"\n"
"%(email)s\n"
"\n"
"하지만 이 이메일 주소를 사용하는 계정이 이미 존재합니다. 혹시 이 사실을 잊으"
"셨다면, 비밀번호 찾기 절차를 통해 계정을 복구해 주세요:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "계정이 이미 존재합니다"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "안녕하세요 %(site_name)s입니다!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"%(site_name)s 서비스를 이용해 주셔서 감사합니다!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"귀하의 계정에 다음과 같은 변경이 이루어졌기 때문에 이 메일을 받으셨습니다:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"이 변경 사항을 인지하지 못하셨다면 즉시 적절한 보안 조치를 취해주시기 바랍니"
"다. 귀하의 계정 변경 출처는 다음과 같습니다:\n"
"\n"
"- IP 주소: %(ip)s\n"
"- 브라우저: %(user_agent)s\n"
"- 날짜: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "귀하의 이메일이 %(from_email)s에서 %(to_email)s(으)로 변경되었습니다."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "이메일 변경됨"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "이메일이 확인 되었습니다."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "이메일 확인"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this email because user %(user_display)s has given your "
#| "email address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"%(user_display)s 에 대해 %(site_domain)s 으로부터 이메일을 인증이 전송되었습"
"니다.\n"
"\n"
"%(activate_url)s 에서 인증을 완료하세요."

#: templates/account/email/email_confirmation_message.txt:7
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"귀하의 로그인 코드는 아래에 나와 있습니다. 열린 브라우저 창에 입력해 주시기 "
"바랍니다."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "이메일 주소를 확인하기 위해, %(activate_url)s을 클릭하세요"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "이메일 주소를 확인하세요."

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "이메일 주소 %(deleted_email)s이(가) 귀하의 계정에서 삭제되었습니다."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "이메일 제거됨"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"귀하의 로그인 코드는 아래에 나와 있습니다. 열린 브라우저 창에 입력해 주시기 "
"바랍니다."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"이 메일은 귀하가 이 작업을 시작하지 않았다면 안전하게 무시하셔도 됩니다."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "로그인 코드"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "비밀번호가 변경되었습니다."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "비밀번호 변경됨"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"귀하의 로그인 코드는 아래에 나와 있습니다. 열린 브라우저 창에 입력해 주시기 "
"바랍니다."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "비밀번호 초기화"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"회원님의 계정에 대한 암호 변경 요청이 접수되었습니다.\n"
"패스워드 초기화를 원치 않는 경우 본 메일을 무시해 주십시요. 변경을 요청할 경"
"우 아래 링크를 클릭하세요."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "잊어버린 경우를 대비하여, 회원님의 사용자 이름은 %(username)s 입니다."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "비밀번호 초기화 이메일"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "비밀번호가 초기화 되었습니다."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "비밀번호가 설정되었습니다."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "비밀번호 설정"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"귀하 또는 다른 사람이 이메일 %(email)s로 계정에 접근하려 했기 때문에 이 이메"
"일을 받으셨습니다. 그러나 저희 데이터베이스에는 해당 계정의 기록이 없습니다."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"이 작업이 귀하에 의해 이루어진 것이라면, 아래 링크를 사용하여 계정을 등록할 "
"수 있습니다."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "알 수 없는 계정"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "이메일 계정"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "현재 이메일"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "변경 중"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "귀하의 이메일 주소는 아직 검증 중입니다."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "변경 취소"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "변경"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "이메일 변경"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "이메일 확인"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"<a href=\"mailto:%(email)s\">%(email)s</a>이 사용자 %(user_display)s의 이메일"
"이 맞는지 확인해 주세요."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "%(email)s은 다른 계정에서 이미 확인되었기 때문에 확인할 수 없습니다."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"이 이메일 확인 링크는 만료되었거나 유효하지 않습니다. <a "
"href=\"%(email_url)s\">새로운 이메일 확인 요청</a>을 해주세요."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "계정이 없다면 %(link)s회원가입%(end_link)s을 진행하세요."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "패스키로 로그인"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "로그인 코드 메일로 받기"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "로그아웃"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "정말로 로그아웃 하시겠습니까?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "주 이메일은 제거할 수 없습니다 (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "%(email)s 으로 확인 메일이 전송되었습니다."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s 을 확인하였습니다."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "%(email)s 을 제거하였습니다."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "%(name)s 으로 로그인 되었습니다."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "로그아웃 되었습니다."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "로그인 코드가 %(email)s로 이메일로 발송되었습니다."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "비밀번호가 성공적으로 변경되었습니다."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "비밀번호가 성공적으로 설정되었습니다."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "로그인 코드가 %(email)s로 이메일로 발송되었습니다."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "주 이메일이 지정되었습니다."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "비밀번호 변경"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "비밀번호를 잊으셨나요?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"비밀번호를 잊으셨나요? 아래에 당신의 이메일을 입력하시면, 비밀번호 초기화 이"
"메일을 전송해 드리겠습니다."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "비밀번호 초기화"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "비밀번호 초기화에 문제가 있으시면 저희에게 연락주세요."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"메일을 전송하였습니다. 메일을 받지 못했다면 스팸 폴더를 확인해주세요.몇 분 후"
"에도 메일을 받지 못하시면 저희에게 연락주세요."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "올비르지 않은 토큰"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"비밀번호 초기화 링크가 올바르지 않습니다. 다시 <a "
"href=\"%(passwd_reset_url)s\">비밀번호 초기화</a> 하세요."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "비밀번호가 변경되었습니다."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "비밀번호 설정"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "변경"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "현재"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "귀하의 이메일 주소는 아직 검증 중입니다."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "패스워드 입력:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr ""
"비밀번호 없이 로그인할 수 있는 특별 코드가 포함된 이메일을 받게 됩니다."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "코드 요청"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "다른 로그인 옵션"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "회원가입"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "회원가입"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "이미 계정이 있으신가요? 바로 %(link)s로그인%(end_link)s 하세요."

#: templates/account/signup.html:39
#, fuzzy
#| msgid "Sign in with a passkey"
msgid "Sign up using a passkey"
msgstr "패스키로 로그인"

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "회원가입"

#: templates/account/signup_by_passkey.html:36
#, fuzzy
#| msgid "Other sign-in options"
msgid "Other options"
msgstr "다른 로그인 옵션"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "회원가입 종료"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "죄송합니다. 회원가입은 현재 종료되었습니다."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "메모"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "%(user_display)s 로 이미 로그인 되어있습니다."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "경고: "

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"등록된 이메일이 없습니다. 알림, 비밀번호 초기화 등을 위해 이메일을 등록해야 "
"합니다."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "이메일을 인증하세요"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"인증 메일이 전송되었습니다. 회원가입 완료를 위해 전송된 메일의 링크를 클릭하"
"세요. 인증 메일을 받지 못했다면 스팸 폴더를 확인해주세요. 몇 분 후에도 메일"
"이 전송되지 않으면 저희에게 연락주세요."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"해당 페이지는 계정 소유주가 맞는지\n"
"확인해야합니다.\n"
"이를 위해 이메일 주소 소유권을 확인해야 합니다."

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"인증 메일이 전송되었습니다. \n"
"회원가입 완료를 위해 전송된 메일의 링크를 클릭하세요. 인증 메일을 받지 못했다"
"면 스팸 폴더를 확인해주세요. 몇 분 후에도 메일이 전송되지 않으면 저희에게 연"
"락주세요."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>메모:</strong> <a href=\"%(email_url)s\">이메일 변경</a>이 가능합니"
"다."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "메시지:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "메뉴:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "계정 연결"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "2단계 인증"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "세션"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"귀하의 계정은 2단계 인증으로 보호되고 있습니다. 인증 코드 입력해 주세요:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "새로운 2단계 인증 복구 코드 세트가 생성되었습니다."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "새로운 복구 코드가 생성되었습니다."

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "인증 앱이 활성화 되었습니다."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "인증 앱이 활성화 되었습니다."

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "인증 앱이 비활성화 되었습니다."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "인증 앱이 비활성화 되었습니다."

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "새로운 보안키가 추가되었습니다."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "보안키 추가됨"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "보안 키가 제거되었습니다."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "보안키 제거됨"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "인증 앱"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "인증 앱을 사용한 인증이 활성화 되었습니다."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "인증 앱이 활성화 되지 않았습니다."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "비활성화"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "활성화"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "보안키"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "%(count)s 를 보안 키로 설정하였습니다."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "보안키가 추가되지 않았습니다."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "관리"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "추가"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "복구 코드"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"총 %(total_count)s개 계정 중 %(unused_count)s개의 계정이 복구 코드 설정 가능"
"합니다."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "복구 코드가 설정되지 않았습니다."

#: templates/mfa/index.html:96
msgid "View"
msgstr "보기"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "다운로드"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "생성"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "새로운 복구 코드 세트가 생성되었습니다."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "보안 키가 추가 되었습니다."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "보안 키가 삭제되었습니다."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "인증 코드 입력:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "귀하의 계정을 위한 새로운 복구 코드 세트를 생성하려고 합니다."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "이 작업은 기존 코드를 무효화 합니다."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "확실하세요?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "사용되지 않은 코드"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "코드 다운로드"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "새로운 코드 생성"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "인증 앱 활성화"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"귀하의 계정을 2단계 인증으로 보호하려면 아래의 QR 코드를 인증기 앱으로 스캔하"
"세요. 그런 다음, 앱에서 생성된 인증 코드를 아래에 입력하세요."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "인증 secret"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"이 secret을 저장해 두면 나중에 인증기 앱을 재설치할 때 사용할 수 있습니다."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "인증 앱 비활성화"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "인증 앱 기반 인증을 비활성화하려고 합니다. 확실하세요?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "보안키 추가"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "보안키 제거"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "정말로 보안키를 제거하시겠습니까?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "사용"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "패스키"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "보안 키"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "이 키는 패스키인지 여부를 나타내지 않습니다."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "지정되지 않음"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "%(created_at)s에 추가됨"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "%(last_used)s에 마지막으로 사용됨"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "변경"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "보안 키 변경"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "저장"

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Passkey"
msgid "Create Passkey"
msgstr "패스키"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"귀하의 계정을 위한 보안 키 인증을 설정하세요. 그리고 나서 보안 키를 추가적으"
"로 설정할 수 있습니다."

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "생성됨"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "이 기능은 자바스크립트를 요구합니다."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "소셜 로그인 실패"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "소셜 계정을 통해 로그인 하는 도중 오류가 발생했습니다."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "다음 소셜 계정들을 통해 로그인 할 수 있습니다:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "해당 계정에 연결되어있는 소셜 계정이 없습니다."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "소셜 계정을 추가하세요."

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "%(provider)s로 부터 소셜 계정이 귀하의 계정에 연결되었습니다."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "소셜 계정 연결됨."

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "%(provider)s로 부터 소셜 계정이 귀하의 계정에서 연결이 해제되었습니다."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "소셜 계정이 연결 해제됨"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "%(provider)s 계정 연결"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "%(provider)s에서 제공하는 소셜 계정을 연결하려 합니다."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "%(provider)s을 통한 로그인"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "소셜 %(provider)s의 계정을 사용해 로그인을 진행하려 합니다."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "계속"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "로그인 취소됨"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"기존 계정중 하나를 사용한 로그인을 취소하였습니다. 실수로 인한 경우, <a "
"href=\"%(login_url)s\">로그인</a>을 진행해 주세요."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "소셜 계정이 연결되었습니다."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "소셜 계정 연결이 해제되었습니다."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"%(provider_name)s 의 계정을 이용하여 %(site_name)s 으로 로그인하려 합니다.\n"
"마지막으로 다음 폼을 작성해주세요:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "또는 소셜을 사용하세요."

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "모든 다른 세션에서 로그아웃되었습니다."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "시작 시작"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP 주소"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "브라우저"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "마지막으로 본 시간"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "현재"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "다른 세션에서 로그아웃"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "사용자 세션"

#: usersessions/models.py:92
msgid "session key"
msgstr "세션 키"

#~ msgid "Account Connection"
#~ msgstr "계정 연결"
