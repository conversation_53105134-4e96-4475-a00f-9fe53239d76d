from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, Branch, UserProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('email', 'first_name', 'last_name', 'role', 'is_active', 'date_joined')
    list_filter = ('role', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('email', 'first_name', 'last_name', 'phone')
    ordering = ('-date_joined',)

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone')}),
        ('Permissions', {'fields': ('role', 'is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'first_name', 'last_name', 'role', 'password1', 'password2'),
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('profile')


@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):
    list_display = ('name', 'city', 'manager', 'is_active', 'created_at')
    list_filter = ('is_active', 'city', 'created_at')
    search_fields = ('name', 'city', 'address')
    ordering = ('name',)

    fieldsets = (
        (None, {'fields': ('name', 'address', 'city')}),
        ('Contact Info', {'fields': ('phone', 'email')}),
        ('Management', {'fields': ('manager', 'is_active')}),
        ('Location', {'fields': ('latitude', 'longitude')}),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('manager')


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'city', 'preferred_branch', 'company_name')
    list_filter = ('city', 'preferred_branch')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'company_name')

    fieldsets = (
        ('User Info', {'fields': ('user',)}),
        ('Address', {'fields': ('address', 'city', 'postal_code')}),
        ('Preferences', {'fields': ('preferred_branch',)}),
        ('Delivery Location', {'fields': ('delivery_latitude', 'delivery_longitude')}),
        ('Profile', {'fields': ('avatar', 'date_of_birth')}),
        ('Business Info', {'fields': ('company_name', 'tax_id')}),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'preferred_branch')
