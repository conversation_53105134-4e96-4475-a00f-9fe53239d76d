# DJANGO-ALLAUTH.
# Copyright (C) 2016
# This file is distributed under the same license as the django-allauth package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-09-26 16:15+0000\n"
"Last-Translator: Ajordat <<EMAIL>>\n"
"Language-Team: Catalan <https://hosted.weblate.org/projects/allauth/django-"
"allauth/ca/>\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.8-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Ara mateix aquest compte està inactiu."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "No podeu eliminar el vostre correu electrònic principal."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Aquest correu electrònic ja està associat amb aquest compte."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr ""
"El correu electrònic i/o la contrasenya que heu especificat no són correctes."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "L'usuari i/o la contrasenya que heu especificat no són correctes."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr ""
"Un usuari ja ha estat registrat amb aquesta direcció de correu electrònic."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Si us plau, escriviu la vostra contrasenya actual."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Codi incorrecte."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Contrasenya actual."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Clau no vàlida o caducada."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "Token invàlid."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "El token per reiniciar la contrasenya no és vàlid."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "No es poden afegit més de %d adreces de correu electrònic."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr ""
"Un usuari ja ha estat registrat amb aquesta direcció de correu electrònic."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Massa intents fallits. Intenteu-ho de nou més tard."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "El correu electrònic no està assignat a cap compte d'usuari"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "El correu electrònic no està assignat a cap compte d'usuari"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "La vostra adreça de correu electrònic principal ha de ser verificada."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Aquest nom d'usuari no pot ser emprat. Si us plau utilitzeu-ne un altre."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "L'usuari i/o la contrasenya que heu especificat no són correctes."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Utilitzeu la vostra paraula de pas"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Utilitzar una aplicació d’autenticació o un codi"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Utilitzar una clau de seguretat"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Marqueu l’adreça de correu seleccionada com a verificada"

#: account/apps.py:11
msgid "Accounts"
msgstr "Comptes"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Heu d'escriure la mateixa contrasenya cada cop."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Contrasenya"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Recordar-me"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Correu electrònic"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "Correu electrònic"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Nom d'usuari"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Iniciar sessió"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Nom d'usuari o correu electrònic"

#: account/forms.py:156
msgid "Username or email"
msgstr "Nom d'usuari o correu electrònic"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Nom d'usuari o correu electrònic"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "Correu electrònic (opcional)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Heu oblidat la contrasenya?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "Correu electrònic (un altre cop)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Confirmació de direcció de correu electrònic"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "Correu electrònic (opcional)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "Correu electrònic (opcional)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Heu d'escriure el mateix correu electrònic cada cop."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Contrasenya (de nou)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Contrasenya actual"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nova contrasenya"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nova contrasenya (de nou)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Codi"

#: account/models.py:26
msgid "user"
msgstr "usuari"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "correu electrònic"

#: account/models.py:34
msgid "verified"
msgstr "verificat"

#: account/models.py:35
msgid "primary"
msgstr "principal"

#: account/models.py:41
msgid "email addresses"
msgstr "correus electrònics"

#: account/models.py:151
msgid "created"
msgstr "creat"

#: account/models.py:152
msgid "sent"
msgstr "enviat"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "clau"

#: account/models.py:158
msgid "email confirmation"
msgstr "confirmació de correu electrònic"

#: account/models.py:159
msgid "email confirmations"
msgstr "confirmacions de correu electrònic"

#: headless/apps.py:7
msgid "Headless"
msgstr ""

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"No podeu afegir una adreça de correu electrònic a un compte protegit per "
"l'autenticació de dos factors."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "No podeu desactivar el doble factor d’autenticació."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"No podeu generar codis de recuperació sense tindre activat el doble factor "
"d’autenticació."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"No podeu activar l'autenticació de dos factors fins que no hàgiu verificat "
"la vostra adreça de correu electrònic."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Clau mestra"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Clau de recanvi"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Clau n. {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Codis de recuperació"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Autenticador TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr ""

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Codi de l’autenticador"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Sense contrasenya"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Habilitar operacions sense contrasenya permet iniciar la sessió utilitzant "
"aquesta clau, però imposa requisits adicionals com la biometrica o protecció "
"per PIN."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Ja existeix un compte associat a aquesta adreça de correu electrònic. Si us "
"plau, primer identifiqueu-vos utilitzant aquest compte, i després vinculeu "
"el vostre compte %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Token invàlid."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "El vostre compte no té una contrasenya definida."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "El vostre compte no té un correu electrònic verificat."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "No podeu desconnectar l'últim dels vostres comptes de tercers."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "El compte de xarxa social ja està connectada a un compte diferent."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Comptes de xarxes socials"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "proveïdor"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID de proveïdor"

#: socialaccount/models.py:56
msgid "name"
msgstr "nom"

#: socialaccount/models.py:58
msgid "client id"
msgstr "identificador client"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "Identificador de App o clau de consumidor"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "clau secreta"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr ""
"frase secrete de API, frase secreta client o frase secreta de consumidor"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Clau"

#: socialaccount/models.py:81
msgid "social application"
msgstr "aplicació de xarxa social"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "aplicacions de xarxes socials"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "darrer inici de sessió"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "data d'incorporació"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "dades extra"

#: socialaccount/models.py:125
msgid "social account"
msgstr "compte de xarxa social"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "comptes de xarxes socials"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) o token d'accés (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "frase secreta de token"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) o token de refrescament (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "expira el"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token d'aplicació de xarxa social"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "tokens d'aplicació de xarxa social"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Dades de perfil invàlides"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Iniciar sessió"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Cancel·lar"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Resposta invàlida a l'hora d'obtenir token des de “%s”. La resposta fou: "
"\"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Resposta invàlida a l'hora d'obtenir token d'accés de \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "No hi ha token de sol·licitud guardat per \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "No hi ha token d'accés guardat per \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Sense accés recursos privats de \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Resposta invàlida a l'hora d'obtenir token de sol·licitud de \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Compte inactiu"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Aquest compte està inactiu."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Hem enviat un codi a %(email_link)s. El codi caduca en breu, així que si us "
"plau introduïu-lo aviat."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Confirmar"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Sol·licitar codi"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Confirmeu l’accés"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr ""
"Sisplau, torneu-vos a autenticar per tal de mantenir el vostre compte segur."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Opcions alternatives"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Verificació de correu electrònic"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Entreu un codi d’autenticació de correu"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "correu electrònic"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Iniciar sessió"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Introduïu el codi d'inici de sessió"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Restablir Contrasenya"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Restablir Contrasenya"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Reenviar Verificació"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "Entreu un codi d’autenticació de correu"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Adreces de correu electrònic"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr ""
"Les següents adreces de correu electrònic estan associades al vostre compte:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Verificat"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Sense verificar"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Principal"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Definir com a principal"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Reenviar Verificació"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Eliminar"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Afegir adreça de correu electrònic"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Afegir correu electrònic"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr ""
"Esteu segurs de voler eliminar l'adreça de correu electrònic seleccionada?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Heu rebut aquest correu electrònic perquè vostè o algú altre s'ha intentat "
"registrar\n"
"per obtenir un compte amb aquest correu:\n"
"\n"
"%(email)s\n"
"\n"
"No obstant això, ja existeix un compte amb aquesta adreça electrònica. En "
"cas que\n"
"ho hàgiu oblidat, utilitzeu el servei de recuperació de contrasenya oblidada "
"per\n"
"recuperar el vostre compte:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "El compte ja existeix"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Hola des de %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Gràcies per utilitzar %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Heu rebut aquest correu perquè el següent canvi ha estat fet al vostre "
"compte:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Si no reconeixeu aquest canvi, premeu precaucions de manera immediata. El "
"canvi ha estat originat des de:\n"
"\n"
"- Adreça IP: %(ip)s\n"
"- Navegador: %(user_agent)s\n"
"- Data: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""
"El vostre correu ha canviat. Correu anterior: %(from_email)s nou correu: "
"%(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Correu electròni canviat"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "El vostre correu ha estat confirmat."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Confirmació de correu electrònic"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Heu rebut aquest missatge perquè l'usuari %(user_display)s ha proporcionat "
"la vostra adreça per registrar un compte a %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"El seu codi de verificació per correu electrònic es troba a continuació. Si "
"us plau, introdueixi'l a la finestra oberta del navegador."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Per confirmar que això és correcte, aneu a %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Si us plau, confirmeu la vostra adreça de correu electrònic"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "El correu %(deleted_email)s s'ha eliminat del vostre compte."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Correu esborrat"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"El seu codi d'inici de sessió es troba a continuació. Si us plau, "
"introdueixi'l a la finestra oberta del navegador."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Aquest correu es pot ignorar de manera segura si vostè no ha iniciat aquesta "
"acció."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Codi d'inici de sessió"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "La vostra contrasenya ha canviat."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "La vostra contrasenya ha canviat"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"El seu codi d'inici de sessió es troba a continuació. Si us plau, "
"introdueixi'l a la finestra oberta del navegador."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Restablir Contrasenya"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Heu rebut aquest correu electrònic perquè vosaltres o una altra persona heu "
"sol·licitat una contrasenya per al vostre compte d'usuari.\n"
"Es pot ignorar de forma segura si no es va sol·licitar el restabliment de "
"contrasenya. Seguiu el següent enllaç per restablir la vostra contrasenya."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "En cas d'haver-lo oblidat, el vostre nom d'usuari és %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Correu electrònic per restablir contrasenya"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "S’ha restablert la vostra contrasenya."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "La vostra contrasenya ha canviat."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Contrasenya establerta"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Està rebent aquest correu perquè vostè, o algú altre, ha intentat accedir a "
"un compte amb el correu %(email)s. No obstant això, no tenim constància de "
"cap compte associat a aquest correu electrònic a la nostra base de dades."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Si era vostè, pot registrar-se per un compte amb l'enllaç a continuació."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Compte desconegut"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Adreces de correu electrònic"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Correu actual"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Canviant a"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr ""
"La vostra adreça de correu electrònic encara està pendent de ser verificada."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Cancel·lar el canvi"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Canviar a"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Canviar correu electrònic"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Confirmar adreça de correu electrònic"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Si us plau confirmeu que <a href=\"mailto:%(email)s\">%(email)s</a> és una "
"adreça de correu electrònic de l'usuari %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"No es pot confirmar %(email)s perquè ja està confirmat en un compte diferent."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Aquest enllaç de verificació de correu electrònic ha expirat o és invàlid. "
"Si us plau, <a href=\"%(email_url)s\">sol·liciteu una nova verificació per "
"correu electrònic.</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Si encara no heu creat un compte, llavors si us plau %(link)sregistreu-"
"vos%(end_link)s primer."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Inicia sessió amb un clau d'accés"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "Enviï'm un codi d'inici de sessió"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Tancar sessió"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Esteu segurs de voler tancar sessió?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "No podeu eliminar el vostre correu electrònic principal (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Correu electrònic de confirmació enviat a %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Heu confirmat %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Eliminat correu electrònic %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Heu iniciat sessió exitosament com a %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Heu tancat sessió."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "S'ha enviat un codi d'inici de sessió a %(email)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Contrasenya canviada amb èxit."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Contrasenya establerta amb èxit."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "S'ha enviat un codi d'inici de sessió a %(email)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Adreça de correu electrònic principal establerta."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Canviar Contrasenya"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Heu oblidat la vostra contrasenya?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Heu oblidat la vostra contrasenya? Introduïu el vostre correu electrònic i "
"us enviarem un correu que us permetrà restablir-la."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Restablir la meva contrasenya"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Si us plau contacteu-nis si teniu algun problema per restablir la vostra "
"contrasenya."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Us hem enviat un correu electrònic. Si us plau contacteu-nos si no el rebeu "
"en uns minuts."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Hi ha un problema amb el token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"L'enllaç per restablir la contrasenya és invàlid, probablement porquè ja ha "
"estat utilitzat. Si us plau soliciteu <a "
"href=\"%(passwd_reset_url)s\">restablir la contrasenya novament</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "La vostra contrasenya ha canviat."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Establir contrasenya"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "Canviar a"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "Actual"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr ""
"La vostra adreça de correu electrònic encara està pendent de ser verificada."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Introduïu la vostra paraula de pas:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr ""
"Rebrà un correu electrònic amb un codi especial per iniciar sessió sense "
"contrasenya."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Sol·licitar codi"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Opcions alternatives d'inici de sessió"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registrar-se"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Registrar-se"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Ja teniu un compte? Si us plau %(link)sinicieu sessió%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Registra't amb un clau d'accés"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Registrar clau d'accés"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Opcions alternatives"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registre tancat"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Ho sentim, en aquest moment el registre está tancat."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Nota"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "ja heu iniciat sessió com a %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Advertència:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Actualment no teniu cap adreça de correu electrònic definida. Hauríeu "
"d'afegir una adreça de correu electrònic per poder rebre notificacions, "
"restablir la contrasenya, etc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Verifiqueu la vostra direcció de correu electrònic"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Us hem enviat un correu electrònic per la seva verificació. Seguiu l'enllaç "
"per completar el procés de registre. Si us plau contacteu-nos si no el rebeu "
"en uns minuts."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Aquesta part del lloc web requereix que verifiquem que\n"
"sou qui dieu ser. Per això us requerim que verifiqueu la\n"
"propietat del vostre correu electrònic. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Us hem enviat un correu electrònic per la vostra\n"
"verificació. Si us plau accediu al link dins el correu electrònic. Si no "
"veieu el correu de verificació a la vostra bústia principal, comproveu la "
"carpeta d'spam. D'altra banda\n"
"contacteu-nos si no el rebeu en uns minuts."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Nota:</strong> encara podeu <a href=\"%(email_url)s\">canviar la "
"vostra adreça de correu electrònic</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Missatges:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menú:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Connexions de Compte"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Autenticació de doble factor (TFA)"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sessions"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"El teu compte està protegit per doble factor d’autenticació. Sisplau, entreu "
"el codi de l’autenticador:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"S’ha generat un nou conjunt de codis de recuperació de doble factor "
"d’autenticació."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "S’ha generat un nou codi de recuperació"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "App d’autenticació activada."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "App d’autenticació activada"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "App d’autenticació desactivada."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "App d’autenticació desactivada"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Una nova clau de seguretat ha estat afegida."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Clau de Seguretat Afegida"

#: templates/mfa/email/webauthn_removed_message.txt:4
#, fuzzy
#| msgid "Your email has been confirmed."
msgid "A security key has been removed."
msgstr "El vostre correu ha estat confirmat."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "App d’autenticació"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "L’autenticació està fent servir una app d’autenticació activa."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "L’app d’autenticació no està activa."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Desactivat"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Activat"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr ""

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr ""

#: templates/mfa/index.html:62
msgid "Manage"
msgstr ""

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr ""

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Codis de recuperació"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Hi ha un %(unused_count)s d’un total de %(total_count)s codi de recuperació "
"disponible."
msgstr[1] ""
"Hi ha %(unused_count)s d’un total de %(total_count)s codis de recuperació "
"disponibles."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "No heu definit codis de recuperació."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Vista"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Descarrega"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generar"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Un nou conjunt de codis de recuperació ha estat generat."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr ""

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr ""

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Entreu un codi d’autenticació:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Esteu a punt de generar un nou conjunt de codis de recuperació pel vostre "
"compte."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Aquesta acció invalidarà els vostres codis existents."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Esteu segur?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Codis no utilitzats"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Descarrega els codis"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Genera nous codis"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Activa la App d’autenticació"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Per protegir el vostre compte amb doble factor d’autenticació, escanejeu el "
"codi QR de sota amb la vostra App d’autenticació. Llavors, introduïu el codi "
"de verificació generat per l’App a sota."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Secret d’autenticador"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Podeu emmagatzemar aquest secret i usar-lo per reinstal·lar la vostra app "
"d’autenticació més endavant."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Desactiva l’App d’autenticació"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Esteu a punt de desactivar la seguretat basada en app d’autenticació. "
"N’esteu segurs?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Add Security Key"
msgstr "clau secreta"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
#, fuzzy
#| msgid "secret key"
msgid "Remove Security Key"
msgstr "clau secreta"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
#, fuzzy
#| msgid "Are you sure you want to sign out?"
msgid "Are you sure you want to remove this security key?"
msgstr "Esteu segurs de voler tancar sessió?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:37
#, fuzzy
#| msgid "secret key"
msgid "Security key"
msgstr "clau secreta"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:41
#, fuzzy
#| msgid "Unverified"
msgid "Unspecified"
msgstr "Sense verificar"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Edit Security Key"
msgstr "clau secreta"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr ""

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Current Password"
msgid "Create Passkey"
msgstr "Contrasenya actual"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "creat"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Ha fallat l’autenticació de tercers"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"S'ha produït un error intentant iniciar sessió a través del vostre compte de "
"xarxa social."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Podeu iniciar sessió amb algun dels següents comptes de tercers:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Actualment no tens cap compte de tercers associat a aquest compte."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Afegir un compte de tercers"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"El compte de tercers des de %(provider)s ha estat vinculat al teu compte."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Compte de tercers vinculat"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"El compte de tercers des de %(provider)s ha estat desvinculat al teu compte."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Compte de tercers desvinculat"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Connectar %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Esteu a punt de connectar un nou compte extern des de %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Iniciar sessió via %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Esteu a punt d'iniciar sessió utilitzant un compte extern des de "
"%(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Continuar"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Inici de sessió cancel·lat"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Heu decidit cancel·lar l'inici de sessió al vostre lloc web utilitzant un "
"dels vostres comptes existents. Si ha estat un error, si us plau <a "
"href=\"%(login_url)s\">inicieu sessió</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "El compte de xarxa social ha estat connectat."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "El compte de xarxa social s'ha desconnectat."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Esteu a punt d'utilitzar el vostre compte de %(provider_name)s per iniciar "
"sessió a\n"
"%(site_name)s. Com a pas final, si us plau completeu el següent formulari:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "O utilitzeu un compte de tercers"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Sortiu de totes les altres sessions."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Ha començat a les"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "Adreça IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Navegador"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Vist darreranent"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Actual"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Tanca les altres sessions"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Sessions de l’usuari"

#: usersessions/models.py:92
msgid "session key"
msgstr "clau de sessió"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Connexions de Compte"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "La contrasenya ha de contenir al menys {0} caràcters."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Has rebut aquest correu electrònic perquè vosaltres o algú altre heu "
#~ "sol·licitat una\n"
#~ "contrasenya per al vostre compte d'usuari. Tot i això, no tenim cap "
#~ "registre d'un usuari\n"
#~ "amb correu electrònic %(email)s a la nostra base de dades.\n"
#~ "\n"
#~ "Aquest correu es pot ignorar de forma segura si no heu sol·licitat un "
#~ "canvi de contrasenya.\n"
#~ "\n"
#~ "Si heu estat vosaltres, podeu registrar un compte d'usuari utilitzant el "
#~ "link de sota."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr ""
#~ "Les següents adreces de correu electrònic estan associades al vostre "
#~ "compte:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Confirmar adreça de correu electrònic"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Si us plau, inicieu sessió amb un\n"
#~ "compte d'una altra xarxa social. O %(link)sregistreu-vos</a> \n"
#~ "com a usuari de %(site_name)s i inicieu sessió a continuació:"

#~ msgid "or"
#~ msgstr "o"

#~ msgid "change password"
#~ msgstr "canviar la contrasenya"

#~ msgid "OpenID Sign In"
#~ msgstr "Iniciar sessió amb OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Aquest correu electrònic ja està associat amb un altre compte."
