from django import forms
from django.core.exceptions import ValidationError
from .models import Product, Category, Subcategory, Brand, Stock, ProductImage


class ProductForm(forms.ModelForm):
    """Form for creating and editing products"""
    
    class Meta:
        model = Product
        fields = [
            'title', 'description', 'category', 'subcategory', 'brand',
            'normal_price', 'reseller_price', 'main_image', 'sku',
            'weight', 'dimensions', 'is_active', 'is_featured',
            'meta_title', 'meta_description'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Product Title'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Product Description'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'subcategory': forms.Select(attrs={'class': 'form-control'}),
            'brand': forms.Select(attrs={'class': 'form-control'}),
            'normal_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'placeholder': 'Normal Price (Dh)'}),
            'reseller_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'placeholder': 'Reseller Price (Dh)'}),
            'main_image': forms.FileInput(attrs={'class': 'form-control'}),
            'sku': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'SKU (Stock Keeping Unit)'}),
            'weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'placeholder': 'Weight (kg)'}),
            'dimensions': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Dimensions (e.g., 10x5x2 cm)'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'meta_title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'SEO Title'}),
            'meta_description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'SEO Description'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)
        self.fields['subcategory'].queryset = Subcategory.objects.filter(is_active=True)
        self.fields['brand'].queryset = Brand.objects.filter(is_active=True)
        
        # Make subcategory optional initially
        self.fields['subcategory'].required = False
        self.fields['brand'].required = False
        
        # Set empty labels
        self.fields['category'].empty_label = "Select Category"
        self.fields['subcategory'].empty_label = "Select Subcategory (Optional)"
        self.fields['brand'].empty_label = "Select Brand (Optional)"
    
    def clean_reseller_price(self):
        normal_price = self.cleaned_data.get('normal_price')
        reseller_price = self.cleaned_data.get('reseller_price')
        
        if normal_price and reseller_price and reseller_price >= normal_price:
            raise ValidationError("Reseller price must be less than normal price.")
        
        return reseller_price
    
    def clean_sku(self):
        sku = self.cleaned_data.get('sku')
        if sku:
            # Check if SKU already exists (excluding current instance if editing)
            existing = Product.objects.filter(sku=sku)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError("A product with this SKU already exists.")
        
        return sku


class CategoryForm(forms.ModelForm):
    """Form for creating and editing categories"""
    
    class Meta:
        model = Category
        fields = ['name', 'description', 'image', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Category Name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Category Description'}),
            'image': forms.FileInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class SubcategoryForm(forms.ModelForm):
    """Form for creating and editing subcategories"""
    
    class Meta:
        model = Subcategory
        fields = ['category', 'name', 'description', 'is_active']
        widgets = {
            'category': forms.Select(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Subcategory Name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Subcategory Description'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)
        self.fields['category'].empty_label = "Select Category"


class BrandForm(forms.ModelForm):
    """Form for creating and editing brands"""
    
    class Meta:
        model = Brand
        fields = ['name', 'description', 'logo', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Brand Name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Brand Description'}),
            'logo': forms.FileInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class StockForm(forms.ModelForm):
    """Form for managing stock levels"""
    
    class Meta:
        model = Stock
        fields = ['quantity', 'minimum_stock', 'maximum_stock']
        widgets = {
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Current Quantity'}),
            'minimum_stock': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Minimum Stock Level'}),
            'maximum_stock': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Maximum Stock Level'}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity')
        minimum_stock = cleaned_data.get('minimum_stock')
        maximum_stock = cleaned_data.get('maximum_stock')
        
        if minimum_stock and maximum_stock and minimum_stock >= maximum_stock:
            raise ValidationError("Minimum stock must be less than maximum stock.")
        
        return cleaned_data


class StockUpdateForm(forms.Form):
    """Form for updating stock quantities"""
    
    OPERATION_CHOICES = (
        ('add', 'Add Stock'),
        ('remove', 'Remove Stock'),
        ('set', 'Set Stock'),
    )
    
    operation = forms.ChoiceField(
        choices=OPERATION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    quantity = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Quantity'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'Notes (optional)'})
    )


class ProductImageForm(forms.ModelForm):
    """Form for adding product images"""
    
    class Meta:
        model = ProductImage
        fields = ['image', 'alt_text', 'is_primary']
        widgets = {
            'image': forms.FileInput(attrs={'class': 'form-control'}),
            'alt_text': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Alt text for image'}),
            'is_primary': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class ProductSearchForm(forms.Form):
    """Form for product search and filtering"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search products...',
            'id': 'search-input'
        })
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        empty_label="All Categories",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    brand = forms.ModelChoiceField(
        queryset=Brand.objects.filter(is_active=True),
        required=False,
        empty_label="All Brands",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[
            ('', 'All Status'),
            ('active', 'Active'),
            ('inactive', 'Inactive'),
            ('featured', 'Featured'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    sort = forms.ChoiceField(
        choices=[
            ('-created_at', 'Newest First'),
            ('created_at', 'Oldest First'),
            ('title', 'Name A-Z'),
            ('-title', 'Name Z-A'),
            ('normal_price', 'Price Low-High'),
            ('-normal_price', 'Price High-Low'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class BulkStockUpdateForm(forms.Form):
    """Form for bulk stock updates"""
    
    products = forms.CharField(widget=forms.HiddenInput())
    operation = forms.ChoiceField(
        choices=[
            ('add', 'Add to Stock'),
            ('remove', 'Remove from Stock'),
            ('set', 'Set Stock Level'),
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    quantity = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Quantity'})
    )
    branch = forms.ModelChoiceField(
        queryset=None,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'Notes (optional)'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from accounts.models import Branch
        self.fields['branch'].queryset = Branch.objects.filter(is_active=True)
        self.fields['branch'].empty_label = "Select Branch"
