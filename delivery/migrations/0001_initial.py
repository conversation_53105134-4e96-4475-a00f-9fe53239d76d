# Generated by Django 5.2.2 on 2025-06-07 22:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DeliveryAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('assigned', 'Assigned'), ('picked', 'Picked'), ('out_for_delivery', 'Out for Delivery'), ('delivered', 'Delivered'), ('failed', 'Failed Delivery'), ('returned', 'Returned')], default='assigned', max_length=20)),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('picked_at', models.DateTimeField(blank=True, null=True)),
                ('out_for_delivery_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('delivery_notes', models.TextField(blank=True)),
                ('customer_signature', models.ImageField(blank=True, null=True, upload_to='signatures/')),
                ('delivery_photo', models.ImageField(blank=True, null=True, upload_to='delivery_photos/')),
                ('failure_reason', models.TextField(blank=True)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_deliveries', to=settings.AUTH_USER_MODEL)),
                ('delivery_person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_assignments', to=settings.AUTH_USER_MODEL)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_assignment', to='orders.order')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('date', models.DateField()),
                ('start_location_latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('start_location_longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('estimated_duration', models.DurationField(blank=True, null=True)),
                ('actual_duration', models.DurationField(blank=True, null=True)),
                ('is_completed', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('delivery_person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_routes', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DeliveryStatusUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('assigned', 'Assigned'), ('picked', 'Picked'), ('out_for_delivery', 'Out for Delivery'), ('delivered', 'Delivered'), ('failed', 'Failed Delivery'), ('returned', 'Returned')], max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('location_latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('location_longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('delivery_assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_updates', to='delivery.deliveryassignment')),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RouteDelivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence_order', models.PositiveIntegerField()),
                ('estimated_arrival', models.DateTimeField(blank=True, null=True)),
                ('actual_arrival', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('delivery_assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='delivery.deliveryassignment')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliveries', to='delivery.deliveryroute')),
            ],
            options={
                'ordering': ['sequence_order'],
                'unique_together': {('route', 'delivery_assignment')},
            },
        ),
    ]
