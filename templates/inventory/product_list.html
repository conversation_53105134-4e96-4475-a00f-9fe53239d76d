{% extends 'base.html' %}

{% block title %}Products - YalaOffice{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-boxes"></i> Products</h1>
            {% if user.is_admin or user.is_manager %}
            <a href="{% url 'inventory:product_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Product
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" placeholder="Search products...">
                    </div>
                    <div class="col-md-2">
                        <select name="category" class="form-control">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="brand" class="form-control">
                            <option value="">All Brands</option>
                            {% for brand in brands %}
                            <option value="{{ brand.id }}" {% if brand_filter == brand.id|stringformat:"s" %}selected{% endif %}>
                                {{ brand.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-control">
                            <option value="">All Status</option>
                            <option value="active" {% if status_filter == "active" %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if status_filter == "inactive" %}selected{% endif %}>Inactive</option>
                            <option value="featured" {% if status_filter == "featured" %}selected{% endif %}>Featured</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Products Grid -->
<div class="row">
    {% for product in page_obj %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="position-relative">
                {% if product.main_image %}
                <img src="{{ product.main_image.url }}" class="card-img-top" alt="{{ product.title }}" style="height: 200px; object-fit: cover;">
                {% else %}
                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                {% endif %}
                
                <!-- Status badges -->
                <div class="position-absolute top-0 end-0 p-2">
                    {% if product.is_featured %}
                    <span class="badge bg-warning">Featured</span>
                    {% endif %}
                    {% if not product.is_active %}
                    <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="card-body d-flex flex-column">
                <h6 class="card-title">{{ product.title }}</h6>
                <p class="card-text text-muted small">{{ product.description|truncatewords:15 }}</p>
                
                <div class="mt-auto">
                    <div class="row mb-2">
                        <div class="col-6">
                            <small class="text-muted">Normal Price:</small><br>
                            <strong>{{ product.normal_price }} Dh</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Reseller Price:</small><br>
                            <strong class="text-success">{{ product.reseller_price }} Dh</strong>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">SKU: {{ product.sku }}</small><br>
                        {% if product.brand %}
                        <small class="text-muted">Brand: {{ product.brand.name }}</small>
                        {% endif %}
                    </div>
                    
                    <!-- Stock Status -->
                    <div class="mb-3">
                        {% with total_stock=product.stock_items.all|length %}
                        {% if total_stock > 0 %}
                        <small class="text-success">
                            <i class="fas fa-check-circle"></i> In Stock ({{ total_stock }} branches)
                        </small>
                        {% else %}
                        <small class="text-danger">
                            <i class="fas fa-times-circle"></i> No Stock
                        </small>
                        {% endif %}
                        {% endwith %}
                    </div>
                    
                    <div class="d-flex gap-2">
                        <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-outline-primary btn-sm flex-fill">
                            <i class="fas fa-eye"></i> View
                        </a>
                        {% if user.is_admin or user.is_manager %}
                        <a href="{% url 'inventory:product_edit' product.id %}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% endif %}
                        {% if user.is_client or user.is_reseller %}
                        <button class="btn btn-success btn-sm" onclick="addToCart({{ product.id }})">
                            <i class="fas fa-cart-plus"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No products found</h5>
            <p class="text-muted">Try adjusting your search criteria or add some products.</p>
            {% if user.is_admin or user.is_manager %}
            <a href="{% url 'inventory:product_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add First Product
            </a>
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="row">
    <div class="col-12">
        <nav aria-label="Products pagination">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">First</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                </li>
                
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Last</a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function addToCart(productId) {
    fetch('/orders/cart/add/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showMessage(data.message, 'success');

            // Update cart badge if exists
            updateCartBadge(data.cart_total_items);
        } else {
            showMessage(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('An error occurred while adding to cart', 'danger');
    });
}

function showMessage(message, type) {
    // Create and show a toast message
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function updateCartBadge(count) {
    const badge = document.querySelector('.cart-badge');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
