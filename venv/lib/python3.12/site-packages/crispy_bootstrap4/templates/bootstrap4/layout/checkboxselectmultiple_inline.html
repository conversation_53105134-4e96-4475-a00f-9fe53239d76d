{% if field.is_hidden %}
    {{ field }}
{% else %}
    <div id="div_{{ field.auto_id }}" class="form-group{% if 'form-horizontal' in form_class %} row{% endif %}{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">

        {% if field.label %}
            <label {% if field.id_for_label %}for="{{ field.id_for_label }}" {% endif %} class="{{ label_class }}{% if not inline_class %} col-form-label{% endif %}{% if field.field.required %} requiredField{% endif %}">
                {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
            </label>
        {% endif %}

        {% include 'bootstrap4/layout/checkboxselectmultiple.html' %}
    </div>
{% endif %}
