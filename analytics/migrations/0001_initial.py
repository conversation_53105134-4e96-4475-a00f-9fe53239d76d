# Generated by Django 5.2.2 on 2025-06-07 22:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True)),
                ('total_users', models.PositiveIntegerField(default=0)),
                ('active_users', models.PositiveIntegerField(default=0)),
                ('new_registrations', models.PositiveIntegerField(default=0)),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('completed_orders', models.PositiveIntegerField(default=0)),
                ('cancelled_orders', models.PositiveIntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_products', models.PositiveIntegerField(default=0)),
                ('active_products', models.PositiveIntegerField(default=0)),
                ('low_stock_products', models.PositiveIntegerField(default=0)),
                ('out_of_stock_products', models.PositiveIntegerField(default=0)),
                ('average_response_time', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('error_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='CustomerAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('average_order_value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('days_since_last_order', models.PositiveIntegerField(default=0)),
                ('login_count', models.PositiveIntegerField(default=0)),
                ('page_views', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to=settings.AUTH_USER_MODEL)),
                ('favorite_brand', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.brand')),
                ('favorite_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.category')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('customer', 'date')},
            },
        ),
        migrations.CreateModel(
            name='InventoryAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('opening_stock', models.PositiveIntegerField(default=0)),
                ('closing_stock', models.PositiveIntegerField(default=0)),
                ('stock_received', models.PositiveIntegerField(default=0)),
                ('stock_sold', models.PositiveIntegerField(default=0)),
                ('stock_adjusted', models.IntegerField(default=0)),
                ('stock_turnover_rate', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('days_of_stock', models.PositiveIntegerField(default=0)),
                ('is_low_stock', models.BooleanField(default=False)),
                ('is_out_of_stock', models.BooleanField(default=False)),
                ('is_overstock', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_analytics', to='accounts.branch')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_analytics', to='inventory.product')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('product', 'branch', 'date')},
            },
        ),
        migrations.CreateModel(
            name='ProductAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('units_sold', models.PositiveIntegerField(default=0)),
                ('revenue', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('profit', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('stock_level', models.PositiveIntegerField(default=0)),
                ('stock_turnover', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('unique_customers', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_analytics', to='accounts.branch')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='inventory.product')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('product', 'branch', 'date')},
            },
        ),
        migrations.CreateModel(
            name='SalesReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_items_sold', models.PositiveIntegerField(default=0)),
                ('average_order_value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('new_customers', models.PositiveIntegerField(default=0)),
                ('returning_customers', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sales_reports', to='accounts.branch')),
                ('top_selling_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.category')),
                ('top_selling_product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.product')),
            ],
            options={
                'ordering': ['-start_date'],
                'unique_together': {('report_type', 'branch', 'start_date', 'end_date')},
            },
        ),
    ]
