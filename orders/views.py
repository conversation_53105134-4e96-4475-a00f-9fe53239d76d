from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods, require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
import json

from .models import Cart, CartItem, Order, OrderItem, Invoice, PromoCode
from inventory.models import Product, Stock
from accounts.models import Branch
from .forms import CheckoutForm, PromoCodeForm, OrderStatusUpdateForm


@login_required
def cart_view(request):
    """View shopping cart"""
    cart, created = Cart.objects.get_or_create(user=request.user)
    cart_items = cart.items.select_related('product').all()

    context = {
        'cart': cart,
        'cart_items': cart_items,
    }
    return render(request, 'orders/cart.html', context)


@login_required
@require_POST
def add_to_cart(request):
    """Add product to cart via AJAX"""
    try:
        data = json.loads(request.body)
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))
        branch_id = data.get('branch_id')

        product = get_object_or_404(Product, id=product_id, is_active=True)

        # Check stock availability if branch is specified
        if branch_id:
            branch = get_object_or_404(Branch, id=branch_id)
            try:
                stock = Stock.objects.get(product=product, branch=branch)
                if stock.available_quantity < quantity:
                    return JsonResponse({
                        'success': False,
                        'message': f'Only {stock.available_quantity} items available in {branch.name}'
                    })
            except Stock.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': f'Product not available in {branch.name}'
                })

        # Get or create cart
        cart, created = Cart.objects.get_or_create(user=request.user)

        # Add or update cart item
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            defaults={'quantity': quantity}
        )

        if not created:
            cart_item.quantity += quantity
            cart_item.save()

        return JsonResponse({
            'success': True,
            'message': f'{product.title} added to cart',
            'cart_total_items': cart.total_items,
            'cart_total_amount': float(cart.total_amount)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_POST
def update_cart_item(request, item_id):
    """Update cart item quantity"""
    try:
        cart_item = get_object_or_404(CartItem, id=item_id, cart__user=request.user)
        data = json.loads(request.body)
        quantity = int(data.get('quantity', 1))

        if quantity <= 0:
            cart_item.delete()
            return JsonResponse({
                'success': True,
                'message': 'Item removed from cart',
                'cart_total_items': cart_item.cart.total_items,
                'cart_total_amount': float(cart_item.cart.total_amount)
            })

        cart_item.quantity = quantity
        cart_item.save()

        return JsonResponse({
            'success': True,
            'message': 'Cart updated',
            'item_total': float(cart_item.get_total_price()),
            'cart_total_items': cart_item.cart.total_items,
            'cart_total_amount': float(cart_item.cart.total_amount)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_POST
def remove_from_cart(request, item_id):
    """Remove item from cart"""
    try:
        cart_item = get_object_or_404(CartItem, id=item_id, cart__user=request.user)
        product_title = cart_item.product.title
        cart_item.delete()

        return JsonResponse({
            'success': True,
            'message': f'{product_title} removed from cart',
            'cart_total_items': cart_item.cart.total_items,
            'cart_total_amount': float(cart_item.cart.total_amount)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def checkout(request):
    """Checkout process"""
    cart = get_object_or_404(Cart, user=request.user)
    cart_items = cart.items.select_related('product').all()

    if not cart_items:
        messages.error(request, 'Your cart is empty.')
        return redirect('orders:cart')

    # Get available branches
    branches = Branch.objects.filter(is_active=True)

    if request.method == 'POST':
        form = CheckoutForm(request.POST)
        promo_form = PromoCodeForm(request.POST)

        if form.is_valid():
            with transaction.atomic():
                # Calculate totals
                subtotal = cart.total_amount
                discount_amount = Decimal('0.00')

                # Apply promo code if provided
                promo_code = None
                if promo_form.is_valid() and promo_form.cleaned_data.get('code'):
                    try:
                        promo_code = PromoCode.objects.get(
                            code=promo_form.cleaned_data['code'],
                            is_active=True
                        )
                        if promo_code.is_valid():
                            discount_amount = promo_code.calculate_discount(subtotal)
                            promo_code.used_count += 1
                            promo_code.save()
                    except PromoCode.DoesNotExist:
                        messages.warning(request, 'Invalid promo code.')

                # Calculate tax (if applicable)
                tax_amount = Decimal('0.00')  # TODO: Implement tax calculation

                total_amount = subtotal - discount_amount + tax_amount

                # Create order
                order = Order.objects.create(
                    user=request.user,
                    branch=form.cleaned_data['branch'],
                    subtotal=subtotal,
                    discount_amount=discount_amount,
                    tax_amount=tax_amount,
                    total_amount=total_amount,
                    payment_method=form.cleaned_data['payment_method'],
                    delivery_address=form.cleaned_data['delivery_address'],
                    delivery_city=form.cleaned_data['delivery_city'],
                    delivery_phone=form.cleaned_data['delivery_phone'],
                    delivery_notes=form.cleaned_data.get('delivery_notes', ''),
                    promo_code=promo_code,
                )

                # Create order items and update stock
                for cart_item in cart_items:
                    # Check stock availability
                    try:
                        stock = Stock.objects.get(
                            product=cart_item.product,
                            branch=order.branch
                        )
                        if stock.available_quantity < cart_item.quantity:
                            raise ValueError(f'Insufficient stock for {cart_item.product.title}')

                        # Reserve stock
                        stock.reserved_quantity += cart_item.quantity
                        stock.save()

                    except Stock.DoesNotExist:
                        raise ValueError(f'{cart_item.product.title} not available in selected branch')

                    # Create order item
                    OrderItem.objects.create(
                        order=order,
                        product=cart_item.product,
                        quantity=cart_item.quantity,
                        unit_price=cart_item.get_unit_price(),
                        total_price=cart_item.get_total_price()
                    )

                # Clear cart
                cart_items.delete()

                messages.success(request, f'Order {order.order_number} placed successfully!')
                return redirect('orders:order_detail', order_id=order.id)

    else:
        # Pre-fill form with user profile data
        initial_data = {}
        if hasattr(request.user, 'profile'):
            profile = request.user.profile
            initial_data = {
                'delivery_address': profile.address,
                'delivery_city': profile.city,
                'delivery_phone': request.user.phone,
                'branch': profile.preferred_branch,
            }

        form = CheckoutForm(initial=initial_data)
        promo_form = PromoCodeForm()

    context = {
        'cart': cart,
        'cart_items': cart_items,
        'form': form,
        'promo_form': promo_form,
        'branches': branches,
    }
    return render(request, 'orders/checkout.html', context)


@login_required
def order_list(request):
    """List user's orders or all orders for admin/manager"""
    if request.user.is_admin or request.user.is_manager:
        orders = Order.objects.select_related('user', 'branch').prefetch_related('items__product')
    else:
        orders = Order.objects.filter(user=request.user).select_related('branch').prefetch_related('items__product')

    # Search and filtering
    search_query = request.GET.get('search', '')
    if search_query:
        orders = orders.filter(
            Q(order_number__icontains=search_query) |
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__email__icontains=search_query)
        )

    status_filter = request.GET.get('status', '')
    if status_filter:
        orders = orders.filter(status=status_filter)

    payment_status_filter = request.GET.get('payment_status', '')
    if payment_status_filter:
        orders = orders.filter(payment_status=payment_status_filter)

    branch_filter = request.GET.get('branch', '')
    if branch_filter:
        orders = orders.filter(branch_id=branch_filter)

    # Date filtering
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        orders = orders.filter(created_at__date__gte=date_from)
    if date_to:
        orders = orders.filter(created_at__date__lte=date_to)

    # Sorting
    sort_by = request.GET.get('sort', '-created_at')
    if sort_by in ['created_at', '-created_at', 'total_amount', '-total_amount', 'status']:
        orders = orders.order_by(sort_by)

    # Pagination
    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    branches = Branch.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'payment_status_filter': payment_status_filter,
        'branch_filter': branch_filter,
        'date_from': date_from,
        'date_to': date_to,
        'sort_by': sort_by,
        'branches': branches,
        'order_statuses': Order.STATUS_CHOICES,
        'payment_statuses': Order.PAYMENT_STATUS,
    }
    return render(request, 'orders/order_list.html', context)


@login_required
def order_detail(request, order_id):
    """Order detail view"""
    if request.user.is_admin or request.user.is_manager:
        order = get_object_or_404(Order, id=order_id)
    else:
        order = get_object_or_404(Order, id=order_id, user=request.user)

    order_items = order.items.select_related('product').all()

    context = {
        'order': order,
        'order_items': order_items,
    }
    return render(request, 'orders/order_detail.html', context)


@login_required
def update_order_status(request, order_id):
    """Update order status (Admin/Manager only)"""
    if not (request.user.is_admin or request.user.is_manager):
        messages.error(request, 'Access denied.')
        return redirect('orders:order_list')

    order = get_object_or_404(Order, id=order_id)

    if request.method == 'POST':
        form = OrderStatusUpdateForm(request.POST, instance=order)
        if form.is_valid():
            old_status = order.status
            old_payment_status = order.payment_status

            order = form.save()

            # Update timestamps based on status changes
            if order.status == 'confirmed' and old_status != 'confirmed':
                order.confirmed_at = timezone.now()
            elif order.status == 'shipped' and old_status != 'shipped':
                order.shipped_at = timezone.now()
            elif order.status == 'delivered' and old_status != 'delivered':
                order.delivered_at = timezone.now()

                # Update stock when order is delivered
                for item in order.items.all():
                    try:
                        stock = Stock.objects.get(product=item.product, branch=order.branch)
                        stock.quantity -= item.quantity
                        stock.reserved_quantity -= item.quantity
                        stock.save()
                    except Stock.DoesNotExist:
                        pass

            order.save()

            # Create audit log entry
            notes = form.cleaned_data.get('notes', '')
            if notes:
                # TODO: Create audit log entry
                pass

            messages.success(request, f'Order {order.order_number} status updated successfully!')
            return redirect('orders:order_detail', order_id=order.id)
    else:
        form = OrderStatusUpdateForm(instance=order)

    context = {
        'form': form,
        'order': order,
    }
    return render(request, 'orders/order_status_update.html', context)


@login_required
@require_POST
def apply_promo_code(request):
    """Apply promo code via AJAX"""
    try:
        data = json.loads(request.body)
        code = data.get('code', '').upper()
        cart_total = Decimal(str(data.get('cart_total', 0)))

        if not code:
            return JsonResponse({
                'success': False,
                'message': 'Please enter a promo code'
            })

        try:
            promo_code = PromoCode.objects.get(code=code)

            if not promo_code.is_valid():
                return JsonResponse({
                    'success': False,
                    'message': 'This promo code is not valid or has expired'
                })

            if cart_total < promo_code.minimum_order_amount:
                return JsonResponse({
                    'success': False,
                    'message': f'Minimum order amount is {promo_code.minimum_order_amount} Dh'
                })

            discount_amount = promo_code.calculate_discount(cart_total)
            new_total = cart_total - discount_amount

            return JsonResponse({
                'success': True,
                'message': f'Promo code applied! You saved {discount_amount} Dh',
                'discount_amount': float(discount_amount),
                'new_total': float(new_total),
                'promo_code': code
            })

        except PromoCode.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Invalid promo code'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })
